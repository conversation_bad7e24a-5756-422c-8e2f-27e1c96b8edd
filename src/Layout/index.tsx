import { FC, useEffect, useState } from "react";
import { Navigate, Outlet, useLocation, useNavigate } from "react-router-dom";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import "./index.less";
import {
  <PERSON><PERSON>crumb,
  Button,
  Dropdown,
  Flex,
  Layout,
  Menu,
  MenuProps,
} from "antd";
import Logo from "@/assets/logo.png";
import { useAuth } from "@/Auth";
import routes from "@/routes";
import { isPathMatchRoute } from "@/utils";
const { Header, Sider, Content } = Layout;
import ResetPasswordModal from "@/components/ResetPasswordModal";

const generateMenuItems = (routes: any, level = 1) => {
  return routes
    .filter(
      (route) => route.name && route.path !== "*" && route.hidden !== true
    )
    .map((route) => {
      const fullPath = route.path;
      const item: any = {
        key: fullPath,
        path: fullPath,
        label: route.name,
        icon: route.icon,
        roles: route.roles,
      };
      if (route.children && level < 2) {
        item.children = generateMenuItems(route.children, level + 1);
      }
      return item;
    });
};

const AppLayout: FC = () => {
  const { pathname } = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] =
    useState(false);

  const navs = generateMenuItems(routes[0].children || []);

  const getSelectedKeys = (menus) => {
    return menus.reduce((memo: string[], menu) => {
      if (pathname.includes(menu.key)) {
        return [...memo, menu.key, ...getSelectedKeys(menu.children || [])];
      } else {
        return memo;
      }
    }, []);
  };

  const getMatchRoutes = (menus) => {
    return menus.reduce((memo: string[], menu) => {
      if (
        pathname.includes(menu.path) ||
        isPathMatchRoute(pathname, menu.path)
      ) {
        return [...memo, menu, ...getMatchRoutes(menu.children || [])];
      } else {
        return memo;
      }
    }, []);
  };

  useEffect(() => {
    const matchedKeys = getSelectedKeys(navs);
    if (matchedKeys.length > 0) {
      setSelectedKeys(matchedKeys);
    }
  }, [pathname]);

  const getBreadcrumbItems = () => {
    const matchRoutes = getMatchRoutes(routes[0].children || []);
    return matchRoutes.map((matchMenu: any, index: number) => {
      const isLast = index === matchRoutes.length - 1;
      return {
        title: isLast ? (
          <Button type="link" style={{ padding: "4px 8px" }}>
            {matchMenu?.name}
          </Button>
        ) : (
          <Button
            type="text"
            style={{ padding: "4px 8px" }}
            onClick={() => {
              if (index === 0) return;
              navigate(matchMenu?.path);
            }}
          >
            {matchMenu?.name}
          </Button>
        ),
      };
    });
  };

  const onMenuClick: MenuProps["onClick"] = (e) => {
    navigate(`${e.key}`);
  };

  const resetPassword = () => {
    setIsResetPasswordModalVisible(true);
  };

  return (
    <Layout className="layout">
      <Sider
        width={240}
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="layout-sider"
      >
        <Flex vertical className="sider-pannel">
          <Flex
            justify="start"
            align="center"
            gap={12}
            className="sider-header"
            style={{
              height: "64px",
              width: "240px",
              background: "#fff",
              color: "#000",
              paddingLeft: "24px",
              borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
            }}
          >
            <img className="logo" style={{ width: "30px" }} src={Logo} alt="" />
            {!collapsed && <h3 className="title">保安考试后台管理系统</h3>}
          </Flex>
          <Flex flex={1} vertical style={{ overflowY: "auto" }}>
            <Menu
              mode="inline"
              style={{ background: "304156" }}
              onClick={onMenuClick}
              selectedKeys={selectedKeys}
              defaultOpenKeys={navs.map((item) => item?.key as string)}
              defaultSelectedKeys={["/home"]}
              items={navs}
            />
          </Flex>
        </Flex>
      </Sider>
      <Layout className="layout-main">
        <Header
          style={{
            padding: "0 12px",
            background: "#fff",
            borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
          }}
        >
          <Flex
            justify="space-between"
            align="center"
            style={{ height: "100%" }}
          >
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: "16px",
                color: "#000",
              }}
            />
            <Dropdown
              menu={{
                onClick: ({ key }) => {
                  if (key === "logout") logout();
                  if (key === "reset") resetPassword();
                },
                items: [
                  user?.type === 1 && {
                    label: "重置密码",
                    key: "reset",
                  },
                  {
                    label: "退出登录",
                    key: "logout",
                  },
                ],
              }}
              trigger={["click"]}
            >
              {user?.name && (
                <Button type="text" style={{ color: "#000" }}>
                  {user?.name}
                </Button>
              )}
            </Dropdown>
          </Flex>
        </Header>
        <Content className="layout-content">
          <div className="layout-breadcrumb">
            <Breadcrumb items={getBreadcrumbItems()} />
          </div>
          <div className="layout-main">
            <Outlet />
          </div>
        </Content>
        <ResetPasswordModal
          id={user?.id}
          isResetPasswordModalVisible={isResetPasswordModalVisible}
          setIsResetPasswordModalVisible={setIsResetPasswordModalVisible}
        />
      </Layout>
      {pathname === "/" && <Navigate to="/home" replace />}
    </Layout>
  );
};

export default AppLayout;
