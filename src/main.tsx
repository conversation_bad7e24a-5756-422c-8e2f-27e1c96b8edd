// import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { Hash<PERSON>outer as Router } from "react-router-dom";
import App from "./App.tsx";
import { ConfigProvider } from "antd";
import { AuthProvider } from "@/Auth/AuthProvider.tsx";
import zhCN from "antd/locale/zh_CN";
import "./index.less";
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

createRoot(document.getElementById("root")!).render(
  <ConfigProvider
    locale={zhCN}
    theme={{
      token: { colorPrimary: "#078AFD" },
      components: {
        Message: {},
      },
    }}
  >
    {/* <StrictMode> */}
    <Router>
      <AuthProvider>
        <App />
      </AuthProvider>
    </Router>
    {/* </StrictMode> */}
  </ConfigProvider>
);
