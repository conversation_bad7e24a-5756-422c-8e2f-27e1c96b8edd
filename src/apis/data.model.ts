import { IPageQueryParams } from "./common.model";

export interface ILoginParam {
  type?: number; //  0 后台用户 1 企业用户
  account: string;
  password: string;
}

export interface IRegisterParam {
  serviceType: string;
  name: string;
  account: string;
  password: string;
}

export interface IUserProfile {
  id: string;
  name: string;
  role: number;
  type: number;
}

export interface ISysUser {
  id: string;
  role: number;
  name: string;
  phone: string;
  password: string;
  organizationName: string;
  operatorTime: number;
}

export interface IExamUserScheduleRecordSummaryItem {
  examUserScheduleId: string;
  level: number;
  levelDesc: string;
  examDate: string;
  startTime: string;
  endTime: string;
  realStartTime: string;
  realEndTime: string;
  score: number;
  physicalStatus: number;
  recordBizStatus: number; // 0未开始 1进行中 2未通过 3已通过 4弃考
  examAddressId: string;
  examAddressName: string;
  admissionNo: string;
  admissionPrintFlag: number;
  admissionPrintTime: number;
  examAttemptCount: number;
  certificateStatus: number;
  certificateNo: string;
  certificateDate: number;
  certificatePrintFlag: number; // 0未打印 1已打印
  receiveStatus: number;
  recipientTime: number;
  recipientName: string;
  recipientPhone: string;
  receiveMethod: string;
  receiveRemark: string;
}

export interface IExamUser {
  id: string;
  registrationNo: string;
  name: string;
  photo: string;
  photoUrl: string;
  sex: number; // 0女 1男
  nationality: string;
  height: string;
  education: string;
  idCard: string;
  phone: string;
  address: string;
  registrationUnit: string;
  workLocation: string;
  bizStatus: number; // 0待审查 1审查通过 2审查未通过
  reviewContent: string;
  remark: string;
  levelStatus: number;
  status: number;
  creator: string;
  createTime: number;
  operator: string;
  operatorTime: number;
  items: Array<IExamUserScheduleRecordSummaryItem>;
}

export interface IExamUserParams extends IPageQueryParams {
  bizStatus?: number;
  search?: string;
}

export interface IExamUserReviewParams {
  ids: Array<string>;
  reviewStatus: number;
  reviewContent?: string;
}

export interface IExamUserScheduleSummary {
  id: string;
  level: number;
  levelDesc: string;
  examAddressId: string;
  examAddressName: string;
  examDate: string;
  startTime: string;
  endTime: string;
  examUserCount: number;
  examUserCertificateCount: number;
}

export interface IExamUserScheduleSummaryParams extends IPageQueryParams {
  examDate?: string;
}

export interface IExamUserScheduleSaveParams {
  id: string;
  level: number;
  examAddressId: string;
  examDate: string;
  startTime: string;
  endTime: string;
}

export interface IExamAddress {
  id: string;
  name: string;
}

export interface ISaveExamUserReceiveCertificateParam {
  type: number; // 0批量 1单个
  id: string;
  recipientName: string;
  recipientPhone: string;
  receiveMethod: number; // 0现场 1邮寄
  receiveRemark: string; // 备注
}

export interface IIdCard {
  address: string; // "广东省深圳市龙华区阳光路28号",
  birthday: string; // null,
  cardID: string; // "******************",
  certType: string; // "居民身份证",
  department: string; // "深圳市公安局龙华分局",
  name: string; // "李泽信",
  nation: string; // "汉",
  photoBase64: string; // "base64"
  readTime: string; // "2025-04-09 11:45:40",
  sex: string; // "男",
  validDate: string; // "2018.07.12-2028.07.12"
}

export interface IMenu {
  code: number;
  seq: number;
  name: string;
}

export interface ISendMessage {
  id: string;
  content: number;
  createTime: string;
}

export interface IMessageItem {
  id: string;
  type: number;
  name: string;
  content: string;
  readFlag: boolean;
  readTime: number;
  createTime: number;
}
export interface IMessageReply {
  id: string;
  content: string;
  items: IMessageItem[];
}

export interface IReplyMessage {
  id: string;
  enterpriseName: string;
  creditCode: string;
  content: string;
  bizStatus: number;
  status: number;
  creator: string;
  createTime: number;
  operator: string;
  operatorTime: number;
}

export interface INoticePublish {
  bizStatus: number;
  id: string;
  noticeType: number;
  publishDepartment: string;
  publishedTime: number;
  readCount: number;
  receiveContent: string;
  receiveType: number;
  title: string;
  unreadCount: number;
}

export interface ISaveNoticeDraft {
  id: string;
  noticeType: number;
  title: string;
  publishDepartment: string;
  content: string;
  receiveType: string;
  receiveContent: string;
  enterpriseIds: string[];
}

export interface ILatestNotice {
  id: string;
  type: number;
  title: string;
  publishedTime: string;
}

export interface ISummaryAnalysis {
  totalExamUser: string;
  examUserApprovedCount: string;
  examUserRejectedCount: string;
  examUserReviewedCount: string;
  examUserPendingReviewCount: string;
  totalExamUserScheduleCount: string;
  totalExamUserRecord: string;
  totalUniqueExamUser: string;
  admissionPrintedCount: string;
  admissionPendingCount: string;
  examPassedCount: string;
  examFailedCount: string;
  examAbsentCount: string;
  certificateCount: string;
  totalApplyEnterprise: string;
  enterprisePendingReviewCount: string;
  enterpriseApprovedCount: string;
  enterpriseRejectedCount: string;
  enterpriseReviewedCount: string;
  totalNotice: string;
  dailyNoticeCount: string;
  investigationNoticeCount: string;
  totalMessage: string;
  repliedMessageCount: string;
  unrepliedMessageCount: string;
  enterpriseLicensedCount: string;
}
// 审批资料相关接口
export interface IEnterpriseRequireMaterials {
  id: string;
  name: string;
  department: string;
  enableFlag: number; // 0启用 1停用
  operator: string;
  operatorTime: number;
}

export interface IEnterpriseRequireMaterialsParams extends IPageQueryParams {
  enableFlag?: number;
}

export interface ISaveEnterpriseRequireMaterials {
  name: string;
  department: string;
}

export interface IEditEnterpriseRequireMaterials {
  id: string;
  name: string;
  department: string;
}

export interface IEnableEnterpriseRequireMaterials {
  id: string;
  enableFlag: number;
}

// 编辑企业账号接口
export interface IEditEnterpriseAccount {
  id: string;
  name: string;
  account: string;
}

// 重置企业账号密码接口
export interface IResetEnterpriseAccountPwd {
  id: string;
  password: string;
}

// 通用ID接口（用于注销和删除等操作）
export interface ICommonId {
  id: string;
}

// 企业客户相关接口
export interface ISaveEnterpriseCustomer {
  enterpriseId: string;
  name: string;
  postCount: number;
  postType: number;
  postTypeCustomize?: string;
  contractStartDate: number; // 时间戳
  contractEndDate: number; // 时间戳
  annualPostAmount: number;
  remark?: string;
}

export interface IEditEnterpriseCustomer {
  id: string;
  enterpriseId: string;
  name: string;
  postCount: number;
  postType: number;
  postTypeCustomize?: string;
  contractStartDate: number; // 时间戳
  contractEndDate: number; // 时间戳
  annualPostAmount: number;
  remark?: string;
}

// 审批流程相关接口
export interface IApprovalFlow {
  id: string;
  type: number; // 0 企业设立审核，1 企业信息变更审核
  serviceType: number;
  serviceTypeName: string;
  flowNode: string;
  operator: string;
  operatorTime: number;
  nodes?: Array<IApprovalFlowNode>;
}

export interface IApprovalFlowNode {
  id?: string;
  role: number;
  seq: number;
}

export interface IApprovalFlowListParams {
  type: number; // 0 企业设立审核，1 企业信息变更审核
}

export interface ISaveApprovalFlow {
  type: number;
  serviceType: number;
  nodes: Array<IApprovalFlowNode>;
}

export interface IEditApprovalFlow {
  id: string;
  type: number;
  serviceType: number;
  nodes: Array<IApprovalFlowNode>;
}

export interface ITrainingAccount {
  id: string;
  name: string;
  account: string;
  bizStatus: number;
  startTime: string;
  endTime: string;
}
// 吊销保安员证书接口
export interface ISaveRevokeEnterpriseSecurityGuard {
  id: string;
  certificateRevokeReason: string;
}

// 保安人员相关接口
export interface ISaveEnterpriseSecurityGuard {
  name: string;
  photo?: string;
  sex: number; // 0女 1男
  nationality: string;
  height?: number;
  education?: number; // 0小学以下 1小学 2初中 3中职 4高中 5大专 6本科 7研究生
  idCard: string;
  phone: string;
  address: string;
  workLocation: string;
  certificateLevel: number; // 1初级 2中级 3高级
  certificatePath: string;
  certificateNo: string;
  remark?: string;
  bizStatus?: number; // 0在职 1离职
  resignedReason?: number; // 离职原因
}

export interface IEditEnterpriseSecurityGuard {
  id: string;
  name: string;
  photo?: string;
  sex: number; // 0女 1男
  nationality: string;
  height?: number;
  education?: number; // 0小学以下 1小学 2初中 3中职 4高中 5大专 6本科 7研究生
  idCard: string;
  phone: string;
  address: string;
  workLocation: string;
  certificateLevel: number; // 1初级 2中级 3高级
  certificatePath: string;
  certificateNo: string;
  remark?: string;
  bizStatus?: number; // 0在职 1离职
  resignedReason?: number; // 离职原因
}
