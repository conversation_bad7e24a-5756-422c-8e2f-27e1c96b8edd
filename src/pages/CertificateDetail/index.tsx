import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  Input,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import "./index.less";
import ReceiveSaveModal from "../Certificate/components/ReceiveSaveModal";
import PrintCertificateButton from "./PrintCertificateButton";
import RegistrationEditModal from "../Registration/components/EditModal";
import PermView from "@/components/PermView";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const CertificateDetail: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isRegistrationEditModalVisible, setIsRegistrationEditModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  const [stat, setStat] = useState("");
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [isReceiveSaveModalVisible, setIsReceiveSaveModalVisible] =
    useState(false);
  const [searchText, setSearchText] = useState("");
  const { examUserScheduleId } = useParams();
  const { user } = useAuth();
  
  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserCertificatePage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        examUserScheduleId:
          examUserScheduleId !== "0" ? examUserScheduleId : null,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));

    if (examUserScheduleId === "0") {
      dataApi.postGetPageStatisticsSummary(5).then((data) => {
        const statText = data.map((x) => `${x.label}：${x.value ?? "-"}`).join("；");
        setStat(statText);
      });
    }
  };

  useEffect(getDataSourceRequest, [pagination, examUserScheduleId]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setIsRegistrationEditModalVisible(true);
  };

  const handleReceiveSave = (record: any) => {
    setCurrentRecord(record);
    setIsReceiveSaveModalVisible(true);
  };

  const afterPrint = () => {
    getDataSourceRequest();
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "证书编号",
      dataIndex: "certificateNo",
      render: (value) => value || "-",
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "保安员等级",
      dataIndex: "levelDesc",
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "报名单位",
      dataIndex: "registrationUnit",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "发证日期",
      dataIndex: "certificateDate",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "是否打印证书",
      dataIndex: "certificatePrintFlag",
      minWidth: 132,
      render: (value) => ({ 0: "否", 1: "是" })[value],
    },
    {
      title: "操作",
      key: "action",
      width: 240,
      hidden: user?.role === 2,
      render: (_, record) => (
        <Flex align="center">
          <PermView types={[0]} roles={[1]}>
            <PrintCertificateButton
              type="link"
              size="small"
              key="print"
              id={record.id}
              afterPrint={afterPrint}
            >
              打印证书
            </PrintCertificateButton>
          </PermView>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="receive"
            onClick={() => handleReceiveSave(record)}
          >
            领取登记
          </PermButton>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            考生详情
          </PermButton>
        </Flex>
      ),
    },
  ];

  return (
    <div className="certificateDetail">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => (
              <Space size="middle">
                {stat && <div className="stat">{stat}</div>}
                <div>共 {x} 条记录</div>
              </Space>
            ),
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <ReceiveSaveModal
        type={1}
        source="receiver"
        id={currentRecord?.id}
        isReceiveSaveModalVisible={isReceiveSaveModalVisible}
        setIsReceiveSaveModalVisible={setIsReceiveSaveModalVisible}
        tableReload={getDataSourceRequest}
      ></ReceiveSaveModal>
      <RegistrationEditModal
        examUserId={currentRecord?.examUserId}
        isEditModalVisible={isRegistrationEditModalVisible}
        setIsEditModalVisible={setIsRegistrationEditModalVisible}
        tableReload={getDataSourceRequest}
        isViewMode
      ></RegistrationEditModal>
    </div>
  );
};

export default CertificateDetail;
