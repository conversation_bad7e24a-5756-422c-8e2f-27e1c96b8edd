import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { Table, Button, Space, Flex, message, Select, Modal } from "antd";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { dataApi } from "@/apis";

const ChangeApply: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [total, setTotal] = useState(0);
  const [changeType, setChangeType] = useState<number | undefined>(undefined);
  const [bizStatus, setBizStatus] = useState<number | undefined>(undefined);

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetChangeApplicationPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        changeType,
        showBizStatus: bizStatus,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleWithdraw = (record: any) => {
    dataApi
      .postWithdrawChangeApplication(record.id)
      .then(() => {
        message.success("操作成功");
        getDataSourceRequest();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: "确认删除",
      content: `确定要删除吗？`,
      onOk: () => {
        dataApi
          .postDelChangeApplication(record.id)
          .then(() => {
            message.success("操作成功");
            getDataSourceRequest();
          })
          .catch((err) => {
            message.error(err.message);
          });
      },
    });
  };

  const handleView = (record: any) => {
    navigate(`/enterprise/changeApply/detail/${record.id}`);
  };

  const handleCreateNew = () => {
    // 创建新的变更申请，跳转到详情页面，不带ID表示新建
    navigate(`/enterprise/changeApply/detail/new`);
  };

  const columns: ColumnsType<any> = [
    {
      title: "序号",
      key: "index",
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: "企业名称",
      dataIndex: "name",
      minWidth: 120,
      render: (value) => value ?? "-",
    },
    {
      title: "统一社会信用代码",
      dataIndex: "creditCode",
      minWidth: 150,
      render: (value) => value ?? "-",
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },
    {
      title: "变更申请类型",
      dataIndex: "changeType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "法人信息变更",
          1: "股东信息变更",
        })[value],
    },
    {
      title: "提交时间",
      dataIndex: "applyTime",
      minWidth: 150,
      render: (value) => (value ? new Date(value).toLocaleString() : "-"),
    },
    {
      title: "状态",
      dataIndex: "showBizStatus",
      minWidth: 120,
      render: (value) =>
        ({
          0: "未提交",
          1: "审查中",
          2: "审查通过",
          3: "审查未通过",
        })[value],
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => handleWithdraw(record)}
          >
            撤回
          </Button>
          <Button type="link" size="small" onClick={() => handleView(record)}>
            详情
          </Button>
          {[0, 1].includes(record?.bizStatus) && (
            <Button
              type="link"
              size="small"
              onClick={() => handleDelete(record)}
            >
              删除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="examination">
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择状态"
            style={{ width: 200 }}
            allowClear
            value={bizStatus}
            onChange={(val) => {
              setBizStatus(val);
              handleSearch();
            }}
            onSelect={handleSearch}
            onClear={() => {
              setBizStatus(undefined);
              handleSearch();
            }}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "未提交" },
              { value: 1, label: "审查中" },
              { value: 2, label: "审查通过" },
              { value: 3, label: "审查未通过" },
            ]}
          />
          <Select
            placeholder="请选择变更类型"
            style={{ width: 200 }}
            allowClear
            value={changeType}
            onChange={(data) => {
              setChangeType(data);
              handleSearch();
            }}
            onSelect={handleSearch}
            onClear={() => {
              setChangeType(undefined);
              handleSearch();
            }}
            options={[
              { value: "", label: "所有类型" },
              { value: 0, label: "法人信息变更" },
              { value: 1, label: "股东信息变更" },
            ]}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button type="primary" onClick={handleCreateNew}>
            信息变更申请
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
    </div>
  );
};

export default ChangeApply;
