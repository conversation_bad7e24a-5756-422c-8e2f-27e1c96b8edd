import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  Button,
  Flex,
  message,
  type TableColumnsType,
  Space,
} from "antd";
import { dataApi } from "@/apis";
import dayjs from "dayjs";

const SetupApproval: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetSetupApplication()
      .then((data) => {
        setLoading(false);
        seteDataSource([data]);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, []);

  const handleWithdraw = (record: any) => {
    dataApi
      .postWithdrawSetupApplication(record.id)
      .then(() => {
        message.success("操作成功");
        getDataSourceRequest();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const handleView = (record: any) => {
    navigate(`/enterprise/setupApply/detail/${record.id}`);
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "企业名称",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "统一社会信用代码",
      dataIndex: "creditCode",
      minWidth: 150,
      render: (value) => value ?? "-",
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },
    {
      title: "提交时间",
      dataIndex: "applyTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "状态",
      dataIndex: "bizStatus",
      minWidth: 90,
      render: (value) =>
        ({
          0: "未提交", // 初始化状态（注册）
          1: "未提交", // 未提交（修改中）
          2: "未提交", // 撤回修改
          3: "审查中", // 待审核（已提交）
          4: "审查通过",
          5: "审查未通过",
        })[value],
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          {record.bizStatus !== 4 && (
            <Button
              type="link"
              size="small"
              key="withdraw"
              onClick={() => handleWithdraw(record)}
            >
              撤回
            </Button>
          )}
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="examination">
      <Flex vertical gap={12}>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
        />
      </Flex>
    </div>
  );
};

export default SetupApproval;
