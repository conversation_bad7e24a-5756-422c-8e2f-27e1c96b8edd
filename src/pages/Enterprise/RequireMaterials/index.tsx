import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>,
  Button,
  Popconfirm,
  message,
  Flex,
  type TableColumnsType,
  Input,
} from "antd";
import { dataApi } from "@/apis";
import { IEnterpriseRequireMaterials } from "@/apis/data.model";
import EditModal from "./components/EditModal";
import dayjs from "dayjs";

const RequireMaterials: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<
    Array<IEnterpriseRequireMaterials>
  >([]);
  const [currentRecord, setCurrentRecord] =
    useState<IEnterpriseRequireMaterials>();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [searchText, setSearchText] = useState("");

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseRequireMaterialsList({
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, []);

  const handleSearch = () => {
    getDataSourceRequest();
  };

  const handleAdd = () => {
    setCurrentRecord(undefined);
    setIsEditModalVisible(true);
  };

  const handleEdit = (record: IEnterpriseRequireMaterials) => {
    setCurrentRecord(record);
    setIsEditModalVisible(true);
  };

  const handleEnable = (record: IEnterpriseRequireMaterials) => {
    const newEnableFlag = record.enableFlag === 0 ? 1 : 0;
    dataApi
      .postEnableEnterpriseRequireMaterials({
        id: record.id,
        enableFlag: newEnableFlag,
      })
      .then(() => {
        message.success("操作成功");
        getDataSourceRequest();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const handleDelete = (record: IEnterpriseRequireMaterials) => {
    dataApi
      .postDelEnterpriseRequireMaterials(record.id)
      .then(() => {
        message.success("操作成功");
        getDataSourceRequest();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const columns: TableColumnsType<IEnterpriseRequireMaterials> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "审核资料名称",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "部门归类",
      dataIndex: "department",
      render: (value) => value ?? "-",
    },
    {
      title: "状态",
      dataIndex: "enableFlag",
      width: 80,
      render: (value) => (value === 0 ? "启用" : "停用"),
    },
    {
      title: "操作人",
      dataIndex: "operator",
      width: 100,
      render: (value) => value ?? "-",
    },
    {
      title: "操作时间",
      dataIndex: "operatorTime",
      width: 160,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => handleEnable(record)}>
            {record.enableFlag === 0 ? "停用" : "启用"}
          </Button>
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确定要删除吗？"
            onConfirm={() => handleDelete(record)}
          >
            <Button type="link" size="small" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="require-materials">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入审核资料名称"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button type="primary" onClick={handleAdd}>
            添加
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
        />
      </Flex>
      <EditModal
        currentRecord={currentRecord}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
      />
    </div>
  );
};

export default RequireMaterials;
