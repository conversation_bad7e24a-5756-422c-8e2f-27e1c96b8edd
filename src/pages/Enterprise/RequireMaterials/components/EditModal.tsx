import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Form, Input, Modal, message } from "antd";
import { dataApi } from "@/apis";
import { IEnterpriseRequireMaterials } from "@/apis/data.model";

interface EditModalProps {
  currentRecord?: IEnterpriseRequireMaterials;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const EditModal: React.FC<EditModalProps> = (props) => {
  const {
    currentRecord,
    isEditModalVisible,
    setIsEditModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isEditModalVisible && currentRecord?.id) {
      form.setFieldsValue(currentRecord);
    }
  }, [isEditModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);

    (currentRecord?.id
      ? dataApi.postEditEnterpriseRequireMaterials(values)
      : dataApi.postSaveEnterpriseRequireMaterials(values)
    )
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title={currentRecord?.id ? "编辑审核资料" : "添加审核资料"}
      open={isEditModalVisible}
      width={500}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
      styles={{
        body: { paddingTop: 24, paddingRight: 12 },
      }}
    >
      <Form
        preserve={false}
        form={form}
        onFinish={onFinish}
        name="modal"
        labelAlign="right"
        labelCol={{ style: { width: 120 } }}
      >
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          name="name"
          label="审核资料名称"
          rules={[{ required: true, message: "请输入审核资料名称" }]}
        >
          <Input placeholder="请输入审核资料名称" />
        </Form.Item>
        <Form.Item
          name="department"
          label="部门归类"
          rules={[{ required: true, message: "请输入部门归类" }]}
        >
          <Input placeholder="请输入部门归类" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditModal;
