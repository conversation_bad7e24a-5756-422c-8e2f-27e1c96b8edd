import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Form,
  Modal,
  message,
  Select,
  Space,
  Card,
  Input,
  Spin,
} from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { dataApi } from "@/apis";
import { IApprovalFlowNode } from "@/apis/data.model";

interface EditModalProps {
  type: number; // 0 企业设立审核，1 企业信息变更审核
  id?: string;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

// 服务类型选项
const serviceTypeOptions = [
  { value: 0, label: "保安培训单位" },
  { value: 1, label: "保安服务公司（保安服务分公司）" },
  { value: 2, label: "武装守护押运" },
  { value: 3, label: "公司自行招用保安员的单位" },
  { value: 4, label: "物业" },
  { value: 5, label: "跨区域保安服务公司" },
];

// 角色选项（根据ConsoleUserRoleEnum）
const roleOptions = [
  { value: 1, label: "一级管理员" },
  { value: 2, label: "二级管理员" },
  // { value: 3, label: "三级管理员" },
];

const EditModal: React.FC<EditModalProps> = (props) => {
  const { type, id, isEditModalVisible, setIsEditModalVisible, tableReload } =
    props;
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [form] = Form.useForm();
  const [nodes, setNodes] = useState<IApprovalFlowNode[]>([]);

  useEffect(() => {
    if (isEditModalVisible) {
      if (id) {
        // 编辑模式：获取数据
        setDataLoading(true);
        dataApi
          .postGetApprovalFlow(id)
          .then((data) => {
            form.setFieldsValue({
              id: data.id,
              serviceType: data.serviceType,
            });
            // 设置节点数据
            setNodes(data.nodes || []);
          })
          .finally(() => {
            setDataLoading(false);
          });
      } else {
        // 新增模式：重置表单和初始化节点
        form.resetFields();
        setNodes([{ role: 1, seq: 1 }]);
      }
    }
  }, [isEditModalVisible, id, form]);

  const onFinish = (values: any) => {
    if (nodes.length === 0) {
      message.error("请至少添加一个审批流程节点");
      return;
    }

    // 验证节点数据
    for (let i = 0; i < nodes.length; i++) {
      if (!nodes[i].role) {
        message.error(`请选择第${i + 1}个节点的角色`);
        return;
      }
    }

    setLoading(true);

    const params = {
      ...values,
      type,
      nodes: nodes.map((node, index) => ({
        ...node,
        seq: index + 1,
      })),
    };

    (id
      ? dataApi.postEditApprovalFlow({ id, ...params })
      : dataApi.postSaveApprovalFlow(params)
    )
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
    setNodes([]);
    setDataLoading(false);
  };

  const addNode = () => {
    setNodes([...nodes, { role: 1, seq: nodes.length + 1 }]);
  };

  const removeNode = (index: number) => {
    if (nodes.length <= 1) {
      message.warning("至少需要保留一个审批节点");
      return;
    }
    const newNodes = nodes.filter((_, i) => i !== index);
    setNodes(newNodes);
  };

  const updateNode = (
    index: number,
    field: keyof IApprovalFlowNode,
    value: any
  ) => {
    setNodes((prev) => {
      const next = [...prev];
      next[index] = { ...next[index], [field]: value };
      return next;
    });
  };

  return (
    <Modal
      title={id ? "编辑审批流程" : "新建审批流程"}
      open={isEditModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
      styles={{
        body: { paddingTop: 24, paddingRight: 12 },
      }}
    >
      <Spin spinning={dataLoading}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 120 } }}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            name="serviceType"
            label="保安服务类型"
            rules={[{ required: true, message: "请选择保安服务类型" }]}
          >
            <Select
              placeholder="请选择保安服务类型"
              options={serviceTypeOptions}
            />
          </Form.Item>

          <Form.Item label="审批流程" required>
            <Space direction="vertical" style={{ width: "100%" }}>
              {nodes.map((node, index) => (
                <Card
                  key={index}
                  size="small"
                  title={`第${index + 1}级审批`}
                  extra={
                    nodes.length > 1 && (
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        onClick={() => removeNode(index)}
                        danger
                      />
                    )
                  }
                >
                  <Select
                    placeholder="请选择角色"
                    value={node.role}
                    onChange={(value) => updateNode(index, "role", value)}
                    options={roleOptions}
                    style={{ width: "100%" }}
                  />
                </Card>
              ))}
              <Button
                type="dashed"
                onClick={addNode}
                icon={<PlusOutlined />}
                style={{ width: "100%" }}
              >
                添加审批节点
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default EditModal;
