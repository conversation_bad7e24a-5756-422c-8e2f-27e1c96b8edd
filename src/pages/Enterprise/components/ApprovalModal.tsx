import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { <PERSON>ert, Button, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface ApprovalModalProps {
  id?: string;
  type: "apply" | "change";
  isApprovalModalVisible: boolean;
  setIsApprovalModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ApprovalModal: React.FC<ApprovalModalProps> = (props) => {
  const {
    id,
    type,
    isApprovalModalVisible,
    setIsApprovalModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {}, [isApprovalModalVisible]);

  const onFinish = (values: any) => {
    Modal.confirm({
      title: "提示",
      content: `此操作不可撤回，请仔细审查核对信息！`,
      onOk: () => {
        setLoading(true);
        (type === "apply"
          ? dataApi.saveSetupApproval({ ...values, id })
          : dataApi.saveChangeApproval({ ...values, id })
        )
          .then(() => {
            message.success("操作成功");
            tableReload();
            onCancel();
          })
          .catch((err) => {
            message.error(err.message);
          })
          .finally(() => setLoading(false));
      },
    });
  };

  const onCancel = () => {
    setIsApprovalModalVisible(false);
    form.resetFields();
  };

  const title = type === "apply" ? "企业设立申请审查" : "企业信息变更申请审查";

  return (
    <Modal
      title={title}
      open={isApprovalModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          保存
        </Button>,
      ]}
    >
      <div style={{ paddingTop: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 120 } }}
        >
          <Form.Item>
            <Alert message={`您正在${title}！`} type="info" showIcon />
          </Form.Item>
          <Form.Item
            name="bizStatus"
            label="审查状态"
            rules={[{ required: true, message: "请选择审查状态" }]}
          >
            <Select
              placeholder="请选择审查状态"
              options={[
                { value: 1, label: "审查通过" },
                { value: 2, label: "审查未通过" },
              ]}
            />
          </Form.Item>

          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.bizStatus !== curValues.bizStatus
            }
          >
            {({ getFieldValue }) =>
              getFieldValue("bizStatus") === 2 ? (
                <Form.Item
                  name="checkContent"
                  label="审查未通过理由"
                  rules={[{ required: true }]}
                >
                  <Input.TextArea rows={2} placeholder="请输入审查未通过理由" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default ApprovalModal;
