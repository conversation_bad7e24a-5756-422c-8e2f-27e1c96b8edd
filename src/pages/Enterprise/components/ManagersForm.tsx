import { useImperativeHandle, forwardRef, useEffect, useState } from "react";
import {
  Form,
  Input,
  Row,
  Col,
  DatePicker,
  Select,
  Divider,
  Flex,
  Typography,
  Button,
  Card,
  Upload,
  Space,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import RegionCascader from "@/components/RegionCascader";
import { fileUploadUrl } from "@/config";

const { Text } = Typography;

// 定义组件对外暴露的方法
export interface ManagersFormMethods {
  validateFields: () => Promise<void>;
  getValues: () => any;
  setFieldsValue: (values: any) => void;
  resetFields: () => void;
  setFieldsError: (
    errors: Array<{ name: string; msg: string; id: string }>
  ) => void;
}

interface ManagersFormProps {
  initialValues?: any[];
  disabled?: boolean;
}

const ManagersForm = forwardRef<ManagersFormMethods, ManagersFormProps>(
  ({ initialValues = [], disabled = false }, ref) => {
    const [form] = Form.useForm();
    const [resumeFilenames, setResumeFilenames] = useState<Array<string>>([]);
    const [resumeUrls, setResumeUrls] = useState<Array<string>>([]);

    // 初始化表单数据
    useEffect(() => {
      if (initialValues && initialValues.length > 0) {
        // 处理日期字段和文件字段
        const formattedValues = initialValues.map((item) => {
          const newItem = { ...item };
          if (newItem.birthDate) {
            newItem.birthDate = dayjs(newItem.birthDate);
          }

          // 处理地区字段
          if (
            newItem.householdProvince &&
            newItem.householdCity &&
            newItem.householdCounty
          ) {
            newItem.householdRegion = [
              newItem.householdProvince,
              newItem.householdCity,
              newItem.householdCounty,
            ];
          }

          if (
            newItem.residenceProvince &&
            newItem.residenceCity &&
            newItem.residenceCounty
          ) {
            newItem.residenceRegion = [
              newItem.residenceProvince,
              newItem.residenceCity,
              newItem.residenceCounty,
            ];
          }

          return newItem;
        });

        // 处理文件
        const formattedResumeFilenames = initialValues.map((item) =>
          item.resumePath ? "简历" + getFilePathExt(item.resumePath) : null
        );
        setResumeFilenames(formattedResumeFilenames);
        const formattedResumeUrls = initialValues.map((item) => item.resumeUrl);
        setResumeUrls(formattedResumeUrls);

        form.setFieldsValue({ managers: formattedValues });
      }
    }, [initialValues, form]);

    // 获取文件扩展名，如 .pdf
    const getFilePathExt = (path) => {
      const start = path.lastIndexOf(".");
      return path.substring(start);
    };

    // 处理文件上传变化
    const handleFileChange = ({ file }, name) => {
      if (file.status == "done") {
        form.setFieldValue(["managers", name, "resumePath"], file.response.data);
        setResumeFilenames((prev) => {
          const next = [...prev];
          next[name] = "简历" + getFilePathExt(file.response.data);
          return next;
        });
        setResumeUrls((prev) => {
          const next = [...prev];
          next[name] = URL.createObjectURL(file.originFileObj);
          return next;
        });
      } else if (file.status == "removed") {
        form.setFieldValue(["managers", name, "resumePath"], null);
        setResumeFilenames((prev) => {
          const next = [...prev];
          next[name] = null;
          return next;
        });
        setResumeUrls((prev) => {
          const next = [...prev];
          next[name] = null;
          return next;
        });
      }
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      validateFields: async () => {
        await form.validateFields();
      },
      getValues: () => {
        const values = form.getFieldsValue(true);

        // 处理管理人员数据
        if (values.managers && Array.isArray(values.managers)) {
          return values.managers.map((manager: any) => {
            const processedManager = { ...manager };

            // 处理地区字段
            if (
              processedManager.householdRegion &&
              processedManager.householdRegion.length === 3
            ) {
              processedManager.householdProvince =
                processedManager.householdRegion[0];
              processedManager.householdCity =
                processedManager.householdRegion[1];
              processedManager.householdCounty =
                processedManager.householdRegion[2];
            }

            if (
              processedManager.residenceRegion &&
              processedManager.residenceRegion.length === 3
            ) {
              processedManager.residenceProvince =
                processedManager.residenceRegion[0];
              processedManager.residenceCity =
                processedManager.residenceRegion[1];
              processedManager.residenceCounty =
                processedManager.residenceRegion[2];
            }

            return processedManager;
          });
        }

        return [];
      },
      setFieldsValue: (values) => {
        if (Array.isArray(values)) {
          form.setFieldsValue({ managers: values });
        }
      },
      resetFields: () => {
        form.resetFields();
        setResumeFilenames([]);
        setResumeUrls([]);
      },
      setFieldsError: (errors) => {
        // 如果没有错误或错误数组为空，清除所有错误
        if (!errors || errors.length === 0) {
          // 清除所有管理人员相关的错误
          const currentValues = form.getFieldsValue();
          const managers = currentValues.managers || [];
          const clearFields = managers.map((_: any, index: number) => ({
            name: ["managers", index],
            errors: [],
          }));
          if (clearFields.length > 0) {
            form.setFields(clearFields);
          }
          return;
        }

        // 根据管理人员ID找到对应的索引，然后设置错误
        const currentValues = form.getFieldsValue();
        const managers = currentValues.managers || [];

        const fields = errors
          .map((error) => {
            // 找到对应管理人员的索引
            const managerIndex = managers.findIndex(
              (m: any) => m.id === error.id
            );
            if (managerIndex !== -1) {
              return {
                name: ["managers", managerIndex, error.name],
                errors: [error.msg],
              };
            }
            return null;
          })
          .filter(Boolean);

        if (fields.length > 0) {
          form.setFields(fields);
        }
      },
    }));

    return (
      <div>
        <div
          style={{
            backgroundColor: "#ffffff",
            position: "sticky",
            top: 0,
            zIndex: 1,
            paddingBottom: 16,
          }}
        >
          <Divider orientation="left">
            <Flex align="baseline" gap={8}>
              <span>管理人员简历</span>
              {!disabled && <Text type="danger">*以下信息均为必填项</Text>}
              {!disabled && (
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  disabled={disabled}
                  onClick={() => {
                    const managers = form.getFieldValue("managers") || [];
                    form.setFieldsValue({
                      managers: [...managers, {}],
                    });
                  }}
                >
                  添加管理人员
                </Button>
              )}
              {!disabled && (
                <Text type="secondary">
                  请点击"添加管理人员"添加管理人员信息
                </Text>
              )}
            </Flex>
          </Divider>
        </div>

        <Form
          form={form}
          layout="horizontal"
          labelCol={{ style: { width: 150 } }}
          disabled={disabled}
        >
          <Form.List name="managers">
            {(fields, { remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    style={{ marginBottom: 16 }}
                    title={`管理人员 ${name + 1}`}
                    size="small"
                    extra={
                      !disabled && (
                        <Button
                          type="link"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => remove(name)}
                        >
                          删除
                        </Button>
                      )
                    }
                  >
                    <Row gutter={[16, 0]}>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="姓名"
                          name={[name, "name"]}
                          rules={[{ required: true, message: "请输入姓名" }]}
                        >
                          <Input placeholder="请输入姓名" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="公民身份号码"
                          name={[name, "idCard"]}
                          rules={[
                            { required: true, message: "请输入公民身份号码" },
                          ]}
                        >
                          <Input placeholder="请输入公民身份号码" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="民族"
                          name={[name, "ethnicity"]}
                          rules={[{ required: true, message: "请输入民族" }]}
                        >
                          <Input placeholder="请输入民族" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[16, 0]}>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="性别"
                          name={[name, "sex"]}
                          rules={[{ required: true, message: "请选择性别" }]}
                        >
                          <Select
                            placeholder="请选择性别"
                            options={[
                              { value: 0, label: "女" },
                              { value: 1, label: "男" },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="出生日期"
                          name={[name, "birthDate"]}
                          rules={[
                            { required: true, message: "请选择出生日期" },
                          ]}
                        >
                          <DatePicker
                            style={{ width: "100%" }}
                            placeholder="请选择出生日期"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="联系电话"
                          name={[name, "phone"]}
                          rules={[
                            { required: true, message: "请输入联系电话" },
                          ]}
                        >
                          <Input placeholder="请输入联系电话" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[16, 0]}>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="职务"
                          name={[name, "position"]}
                          rules={[{ required: true, message: "请输入职务" }]}
                        >
                          <Input placeholder="请输入职务" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="兵役情况"
                          name={[name, "militaryStatus"]}
                          rules={[
                            { required: true, message: "请选择兵役情况" },
                          ]}
                        >
                          <Select
                            placeholder="请选择兵役情况"
                            options={[
                              { value: 0, label: "未服兵役" },
                              { value: 1, label: "现役军人" },
                              { value: 2, label: "服预备役" },
                              { value: 3, label: "已退役" },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="文化程度"
                          name={[name, "educationLevel"]}
                          rules={[
                            { required: true, message: "请选择文化程度" },
                          ]}
                        >
                          <Select
                            placeholder="请选择文化程度"
                            options={[
                              { value: 0, label: "小学以下" },
                              { value: 1, label: "小学" },
                              { value: 2, label: "初中" },
                              { value: 3, label: "中职" },
                              { value: 4, label: "高中" },
                              { value: 5, label: "大专" },
                              { value: 6, label: "本科" },
                              { value: 7, label: "研究生" },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[16, 0]}>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="现住址省市县（区）"
                          name={[name, "residenceRegion"]}
                          rules={[
                            { required: true, message: "请选择现住址省市县" },
                          ]}
                        >
                          <RegionCascader placeholder="请选择省/市/县" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="现住址详细地址"
                          name={[name, "residenceAddress"]}
                          rules={[
                            { required: true, message: "请输入现住址详细地址" },
                          ]}
                        >
                          <Input placeholder="请输入现住址详细地址" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[16, 0]}>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="户籍地省市县（区）"
                          name={[name, "householdRegion"]}
                          rules={[
                            { required: true, message: "请选择户籍地省市县" },
                          ]}
                        >
                          <RegionCascader placeholder="请选择省/市/县" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          label="户籍地详细地址"
                          name={[name, "householdAddress"]}
                          rules={[
                            { required: true, message: "请输入户籍地详细地址" },
                          ]}
                        >
                          <Input placeholder="请输入户籍地详细地址" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[16, 0]}>
                      <Col span={16}>
                        <Form.Item
                          {...restField}
                          label="简历"
                          name={[name, "resumePath"]}
                          rules={[{ required: true, message: "请上传简历" }]}
                        >
                          <Space>
                            {resumeFilenames[name] ? (
                              <Button
                                type="link"
                                target="_blank"
                                href={resumeUrls[name]}
                                disabled={false}
                              >
                                {resumeFilenames[name]}
                              </Button>
                            ) : (
                              <span>未上传文件</span>
                            )}
                            <Upload
                              action={fileUploadUrl}
                              data={{ type: 3 }}
                              accept=".pdf,.doc,.docx"
                              maxCount={1}
                              showUploadList={false}
                              onChange={(info) => handleFileChange(info, name)}
                            >
                              <Button icon={<UploadOutlined />}>
                                点击上传
                              </Button>
                            </Upload>
                          </Space>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
              </>
            )}
          </Form.List>
        </Form>
      </div>
    );
  }
);

export default ManagersForm;
