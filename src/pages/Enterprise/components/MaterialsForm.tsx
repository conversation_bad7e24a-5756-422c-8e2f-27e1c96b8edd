import { useImperative<PERSON><PERSON><PERSON>, forwardRef, useEffect, useState } from "react";
import {
  Button,
  Space,
  Divider,
  message,
  Table,
  Upload,
  Typography,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import { fileUploadWithNameUrl } from "@/config";

// 定义组件对外暴露的方法
export interface MaterialsFormMethods {
  validateFields: () => Promise<void>;
  getValues: () => any;
  setFieldsValue: (values: any) => void;
  resetFields: () => void;
  setFieldsError: (
    errors: Array<{ name: string; msg: string; id: string }>
  ) => void;
}

interface MaterialsFormProps {
  initialValues?: any[];
  disabled?: boolean;
}

// 定义材料数据结构
interface MaterialData {
  id: string;
  requireMaterialsName: string;
  materialsName?: string;
  materialsUrl?: string;
  materialsPath?: string;
}

const MaterialsForm = forwardRef<MaterialsFormMethods, MaterialsFormProps>(
  ({ initialValues = [], disabled = false }, ref) => {
    const [dataSource, setDataSource] = useState<MaterialData[]>([]);
    const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

    // 初始化数据
    useEffect(() => {
      if (initialValues && initialValues.length > 0) {
        setDataSource(initialValues);
      }
    }, [initialValues]);

    // 处理文件上传变化
    const handleFileChange = (info: any, record: MaterialData) => {
      const { file } = info;
      if (file.status === "done") {
        // 从响应中获取文件名和路径
        const { fileName, path } = file.response.data;
        const updatedData = dataSource.map((item) =>
          item.id === record.id
            ? {
                ...item,
                materialsName: fileName,
                materialsPath: path,
                materialsUrl: URL.createObjectURL(file.originFileObj),
              }
            : item
        );
        setDataSource(updatedData);
        setFieldErrors((prev) => {
          delete prev[record.id];
          return prev;
        });
        message.success("文件上传成功");
      } else if (file.status === "error") {
        message.error("文件上传失败");
      }
    };

    // 处理文件删除
    const handleDelete = (record: MaterialData) => {
      const updatedData = dataSource.map((item) =>
        item.id === record.id
          ? {
              ...item,
              materialsName: "",
              materialsPath: "",
              materialsUrl: "",
            }
          : item
      );
      setDataSource(updatedData);
      message.success("文件删除成功");
    };

    // 处理文件查看
    const handleView = (record: MaterialData) => {
      if (record.materialsUrl) {
        window.open(record.materialsUrl, "_blank");
      } else {
        message.warning("暂无文件可查看");
      }
    };

    // 定义表格列
    const columns: ColumnsType<MaterialData> = [
      {
        title: "审核资料名称",
        dataIndex: "requireMaterialsName",
        key: "requireMaterialsName",
        render: (text, record) => {
          const hasError = fieldErrors[record.id];
          return (
            <Typography.Text type={hasError ? "danger" : undefined}>
              {text || "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "上传文件",
        dataIndex: "materialsName",
        key: "materialsName",
        render: (text, record) => {
          if (text && record.materialsUrl) {
            return (
              <Button
                type="link"
                onClick={() => window.open(record.materialsUrl, "_blank")}
              >
                {text}
              </Button>
            );
          }
          const hasError = fieldErrors[record.id];
          if (hasError) {
            return (
              <Typography.Text type="danger" style={{ padding: "0 15px" }}>
                {hasError}
              </Typography.Text>
            );
          }
          return <div style={{ padding: "0 15px" }}>-</div>;
        },
      },
      {
        title: "操作",
        key: "action",
        render: (_, record) => (
          <Space>
            {!disabled && (
              <Upload
                action={fileUploadWithNameUrl}
                data={{ type: 5 }}
                accept=".pdf,.jpg,.jpeg,.png"
                maxCount={1}
                showUploadList={false}
                onChange={(info) => handleFileChange(info, record)}
              >
                <Button type="link" size="small">
                  上传
                </Button>
              </Upload>
            )}
            {!disabled && (
              <Button
                type="link"
                size="small"
                onClick={() => handleDelete(record)}
                disabled={!record.materialsName}
              >
                删除
              </Button>
            )}
            <Button
              type="link"
              size="small"
              onClick={() => handleView(record)}
              disabled={!record.materialsUrl}
            >
              查看
            </Button>
          </Space>
        ),
      },
    ];

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      validateFields: async () => {
        // MaterialsForm没有必填项，所以校验方法为空
      },
      getValues: () => {
        return dataSource; // 直接返回数组，避免双重嵌套
      },
      setFieldsValue: (values) => {
        if (values.materials) {
          setDataSource(values.materials);
        }
      },
      resetFields: () => {
        setDataSource([]);
        setFieldErrors({});
      },
      setFieldsError: (errors) => {
        // 为MaterialsForm设置错误状态
        if (!errors || errors.length === 0) {
          // 清除所有错误
          setFieldErrors({});
          return;
        }

        const newErrors: Record<string, string> = {};
        errors.forEach((error) => {
          newErrors[error.id] = error.msg;
        });
        console.log("newErrors", newErrors);
        setFieldErrors(newErrors);
      },
    }));

    return (
      <div>
        <div
          style={{
            backgroundColor: "#ffffff",
            position: "sticky",
            top: 0,
            zIndex: 1,
            paddingBottom: 16,
          }}
        >
          <Divider orientation="left">电子资料上传</Divider>
        </div>

        <Table
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </div>
    );
  }
);

export default MaterialsForm;
