import { useImperativeHandle, forwardRef, useEffect } from "react";
import {
  Form,
  Input,
  Row,
  Col,
  DatePicker,
  Select,
  Divider,
  Flex,
  Typography,
  InputNumber,
  <PERSON><PERSON>,
  Card,
} from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import RegionCascader from "@/components/RegionCascader";

const { Text } = Typography;

// 定义组件对外暴露的方法
export interface ShareholdersFormMethods {
  validateFields: () => Promise<void>;
  getValues: () => any;
  setFieldsValue: (values: any) => void;
  resetFields: () => void;
  setFieldsError: (
    errors: Array<{ name: string; msg: string; id: string }>
  ) => void;
}

interface ShareholdersFormProps {
  initialValues?: any[];
  disabled?: boolean;
}

const ShareholdersForm = forwardRef<
  ShareholdersFormMethods,
  ShareholdersFormProps
>(({ initialValues = [], disabled = false }, ref) => {
  const [form] = Form.useForm();

  // 初始化表单数据
  useEffect(() => {
    if (initialValues && initialValues.length > 0) {
      // 处理日期字段
      const formattedValues = initialValues.map((item) => {
        const newItem = { ...item };
        if (newItem.birthDate) {
          newItem.birthDate = dayjs(newItem.birthDate);
        }

        // 处理地区字段
        if (
          newItem.householdProvince &&
          newItem.householdCity &&
          newItem.householdCounty
        ) {
          newItem.householdRegion = [
            newItem.householdProvince,
            newItem.householdCity,
            newItem.householdCounty,
          ];
        }

        if (
          newItem.residenceProvince &&
          newItem.residenceCity &&
          newItem.residenceCounty
        ) {
          newItem.residenceRegion = [
            newItem.residenceProvince,
            newItem.residenceCity,
            newItem.residenceCounty,
          ];
        }

        return newItem;
      });
      form.setFieldsValue({ shareholders: formattedValues });
    }
  }, [initialValues, form]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      await form.validateFields();
    },
    getValues: () => {
      const values = form.getFieldsValue(true);

      // 处理股东数据
      if (values.shareholders && Array.isArray(values.shareholders)) {
        return values.shareholders.map((shareholder: any) => {
          const processedShareholder = { ...shareholder };

          // 处理地区字段
          if (
            processedShareholder.householdRegion &&
            processedShareholder.householdRegion.length === 3
          ) {
            processedShareholder.householdProvince =
              processedShareholder.householdRegion[0];
            processedShareholder.householdCity =
              processedShareholder.householdRegion[1];
            processedShareholder.householdCounty =
              processedShareholder.householdRegion[2];
          }

          if (
            processedShareholder.residenceRegion &&
            processedShareholder.residenceRegion.length === 3
          ) {
            processedShareholder.residenceProvince =
              processedShareholder.residenceRegion[0];
            processedShareholder.residenceCity =
              processedShareholder.residenceRegion[1];
            processedShareholder.residenceCounty =
              processedShareholder.residenceRegion[2];
          }

          return processedShareholder;
        });
      }

      return [];
    },
    setFieldsValue: (values) => {
      if (Array.isArray(values)) {
        form.setFieldsValue({ shareholders: values });
      }
    },
    resetFields: () => {
      form.resetFields();
    },
    setFieldsError: (errors) => {
      // 如果没有错误或错误数组为空，清除所有错误
      if (!errors || errors.length === 0) {
        // 清除所有股东相关的错误
        const currentValues = form.getFieldsValue();
        const shareholders = currentValues.shareholders || [];
        const clearFields = shareholders.map((_: any, index: number) => ({
          name: ["shareholders", index],
          errors: [],
        }));
        if (clearFields.length > 0) {
          form.setFields(clearFields);
        }
        return;
      }

      // 根据股东ID找到对应的索引，然后设置错误
      const currentValues = form.getFieldsValue();
      const shareholders = currentValues.shareholders || [];

      const fields = errors
        .map((error) => {
          // 找到对应股东的索引
          const shareholderIndex = shareholders.findIndex(
            (s: any) => s.id === error.id
          );
          if (shareholderIndex !== -1) {
            return {
              name: ["shareholders", shareholderIndex, error.name],
              errors: [error.msg],
            };
          }
          return null;
        })
        .filter(Boolean);

      if (fields.length > 0) {
        form.setFields(fields);
      }
    },
  }));

  return (
    <div>
      <div
        style={{
          backgroundColor: "#ffffff",
          position: "sticky",
          top: 0,
          zIndex: 1,
          paddingBottom: 16,
        }}
      >
        <Divider orientation="left">
          <Flex align="baseline" gap={8}>
            <span>股东基本信息</span>
            {!disabled && (
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                disabled={disabled}
                onClick={() => {
                  const shareholders = form.getFieldValue("shareholders") || [];
                  form.setFieldsValue({
                    shareholders: [...shareholders, {}],
                  });
                }}
              >
                添加股东
              </Button>
            )}
            {!disabled && (
              <Text type="secondary">请点击"添加股东"添加股东信息</Text>
            )}
            {!disabled && (
              <Text type="danger">
                若您的企业存在股东，请添加股东信息，所有标有 * 的均为必填项
              </Text>
            )}
          </Flex>
        </Divider>
      </div>

      <Form
        form={form}
        layout="horizontal"
        labelCol={{ style: { width: 150 } }}
        disabled={disabled}
      >
        <Form.List name="shareholders">
          {(fields, { remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Card
                  key={key}
                  style={{ marginBottom: 16 }}
                  title={`股东 ${name + 1}`}
                  size="small"
                  extra={
                    !disabled && (
                      <Button
                        type="link"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => remove(name)}
                      >
                        删除
                      </Button>
                    )
                  }
                >
                  <Row gutter={[16, 0]}>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="股东类型"
                        name={[name, "type"]}
                        rules={[{ required: true, message: "请选择股东类型" }]}
                      >
                        <Select
                          placeholder="请选择股东类型"
                          options={[
                            { value: 0, label: "自然人" },
                            { value: 1, label: "其他" },
                          ]}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="出资金额（元）"
                        name={[name, "investmentAmount"]}
                        rules={[{ required: true, message: "请输入出资金额" }]}
                      >
                        <InputNumber
                          style={{ width: "100%" }}
                          placeholder="请输入出资金额"
                          precision={2}
                          min={0}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="所占股份百分比（%）"
                        name={[name, "shareholdingRatio"]}
                        rules={[
                          { required: true, message: "请输入所占股份百分比" },
                        ]}
                      >
                        <InputNumber
                          style={{ width: "100%" }}
                          placeholder="请输入所占股份百分比"
                          precision={2}
                          min={0}
                          max={100}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={[16, 0]}>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="姓名"
                        name={[name, "name"]}
                        rules={[{ required: true, message: "请输入姓名" }]}
                      >
                        <Input placeholder="请输入姓名" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="公民身份号码"
                        name={[name, "idCard"]}
                        rules={[
                          { required: true, message: "请输入公民身份号码" },
                        ]}
                      >
                        <Input placeholder="请输入公民身份号码" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="性别"
                        name={[name, "sex"]}
                        rules={[{ required: true, message: "请选择性别" }]}
                      >
                        <Select
                          placeholder="请选择性别"
                          options={[
                            { value: 0, label: "女" },
                            { value: 1, label: "男" },
                          ]}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={[16, 0]}>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="出生日期"
                        name={[name, "birthDate"]}
                        rules={[{ required: true, message: "请选择出生日期" }]}
                      >
                        <DatePicker
                          style={{ width: "100%" }}
                          placeholder="请选择出生日期"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="民族"
                        name={[name, "ethnicity"]}
                        rules={[{ required: true, message: "请输入民族" }]}
                      >
                        <Input placeholder="请输入民族" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="联系电话"
                        name={[name, "phone"]}
                        rules={[{ required: true, message: "请输入联系电话" }]}
                      >
                        <Input placeholder="请输入联系电话" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={[16, 0]}>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="户籍地省市县（区）"
                        name={[name, "householdRegion"]}
                        rules={[
                          { required: true, message: "请选择户籍地省市县" },
                        ]}
                      >
                        <RegionCascader placeholder="请选择省/市/县" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="户籍地详细地址"
                        name={[name, "householdAddress"]}
                        rules={[
                          { required: true, message: "请输入户籍地详细地址" },
                        ]}
                      >
                        <Input placeholder="请输入户籍地详细地址" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={[16, 0]}>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="现住址省市县（区）"
                        name={[name, "residenceRegion"]}
                        rules={[
                          { required: true, message: "请选择现住址省市县" },
                        ]}
                      >
                        <RegionCascader placeholder="请选择省/市/县" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        label="现住址详细地址"
                        name={[name, "residenceAddress"]}
                        rules={[
                          { required: true, message: "请输入现住址详细地址" },
                        ]}
                      >
                        <Input placeholder="请输入现住址详细地址" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              ))}
            </>
          )}
        </Form.List>
      </Form>
    </div>
  );
});

export default ShareholdersForm;
