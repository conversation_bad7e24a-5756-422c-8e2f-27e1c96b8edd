import { FC, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Flex,
  message,
  Radio,
  Row,
  Space,
  Typography,
} from "antd";
import "./index.less";
import TextPanel from "./components/TextPanel";
import { AuditOutlined, CaretUpFilled } from "@ant-design/icons";
import classNames from "classnames";
import { getOption } from "./options";
import * as echarts from "echarts";
import dayjs from "dayjs";
import EditPublishModal from "../Notification/Publish/components/EditPublishModal";
import { data, useNavigate } from "react-router-dom";
import { dataApi } from "@/apis";
import { ILatestNotice, ISummaryAnalysis } from "@/apis/data.model";
import PermView from "@/components/PermView";
import { useAuth } from "@/Auth";

const Home: FC = () => {
  const [myChart, setMyChart] = useState<echarts.ECharts>();
  const [radioValue, setRadioValue] = useState("day");
  const [dateRanges, setDateRanges] = useState<any>([]);
  const [isEditPublishVisible, setIsEditPublishVisible] = useState(false);
  const [latestNotices, setLatestNotices] = useState<ILatestNotice[]>([]);
  const [currentLatestNotice, setCurrentLatestNotice] = useState<ILatestNotice>(
    {} as ILatestNotice
  );
  const [summaryAnalysis, setSummaryAnalysis] = useState<ISummaryAnalysis>();

  const navigate = useNavigate();
  const { user } = useAuth();
  const chartId = `graph`;

  // useEffect(() => {
  //   if (user?.role === 2) return;
  //   const chart = echarts.init(document.getElementById(chartId) as HTMLElement);
  //   setMyChart(chart);
  //   const resizeHandler = () => {
  //     chart.resize();
  //   };
  //   window.addEventListener("resize", resizeHandler);
  //   return () => {
  //     window.removeEventListener("resize", resizeHandler);
  //     chart.dispose();
  //   };
  // }, []);

  useEffect(() => {
    dataApi.getLatestContentList().then((data) => {
      setLatestNotices(data);
    });
    dataApi.getSummaryAnalysis().then((data) => {
      setSummaryAnalysis(data);
    });
  }, []);

  useEffect(() => {
    if (!myChart) return;
    if (user?.role === 2) return;
    const historys = [
      {
        gpuType: "3090",
        history: [
          {
            time: "2025-05-01 00:00",
            cost: 1265.0,
          },
          {
            time: "2025-05-02 00:00",
            cost: 1265.0,
          },
          {
            time: "2025-05-03 00:00",
            cost: 1265.0,
          },
          {
            time: "2025-05-04 00:00",
            cost: 1265.0,
          },
          {
            time: "2025-05-05 00:00",
            cost: 1265.0,
          },
          {
            time: "2025-05-06 00:00",
            cost: 1265.0,
          },
          {
            time: "2025-05-07 00:00",
            cost: 1449.5333251953125,
          },
          {
            time: "2025-05-08 00:00",
            cost: 1655.6499862670898,
          },
          {
            time: "2025-05-09 00:00",
            cost: 1656.6666641235352,
          },
          {
            time: "2025-05-10 00:00",
            cost: 1655.933334350586,
          },
          {
            time: "2025-05-11 00:00",
            cost: 1656.0,
          },
          {
            time: "2025-05-12 00:00",
            cost: 1665.2333394289017,
          },
          {
            time: "2025-05-13 00:00",
            cost: 1809.0666666701436,
          },
          {
            time: "2025-05-14 00:00",
            cost: 1846.0166621096432,
          },
          {
            time: "2025-05-15 00:00",
            cost: 1769.199997484684,
          },
          {
            time: "2025-05-16 00:00",
            cost: 1830.6000108718872,
          },
          {
            time: "2025-05-17 00:00",
            cost: 2082.333335876465,
          },
          {
            time: "2025-05-18 00:00",
            cost: 2138.7333374023438,
          },
          {
            time: "2025-05-19 00:00",
            cost: 2021.883335545659,
          },
          {
            time: "2025-05-20 00:00",
            cost: 1982.6833209320903,
          },
          {
            time: "2025-05-21 00:00",
            cost: 2035.366666354239,
          },
          {
            time: "2025-05-22 00:00",
            cost: 1845.6333287879825,
          },
          {
            time: "2025-05-23 00:00",
            cost: 1828.8833281472325,
          },
          {
            time: "2025-05-24 00:00",
            cost: 1748.0,
          },
          {
            time: "2025-05-25 00:00",
            cost: 1748.0,
          },
          {
            time: "2025-05-26 00:00",
            cost: 1741.0500029623508,
          },
          {
            time: "2025-05-27 00:00",
            cost: 1744.9000000953674,
          },
          {
            time: "2025-05-28 00:00",
            cost: 1766.5,
          },
          {
            time: "2025-05-29 00:00",
            cost: 1854.9166641235352,
          },
          {
            time: "2025-05-30 00:00",
            cost: 1840.0,
          },
        ],
      },
    ];
    const option = getOption(historys);
    myChart.setOption(option, { notMerge: true });
  }, [myChart]);

  useEffect(() => {
    if (radioValue === "day") {
      setDateRanges([dayjs(), dayjs()]);
    }
    if (radioValue === "week") {
      setDateRanges([dayjs().startOf("week"), dayjs().endOf("week")]);
    }
    if (radioValue === "month") {
      setDateRanges([dayjs().startOf("month"), dayjs().endOf("month")]);
    }
    if (radioValue === "year") {
      setDateRanges([dayjs().startOf("year"), dayjs().endOf("year")]);
    }
  }, [radioValue]);

  const getLatestContentType = (latestNotice: ILatestNotice) => {
    switch (latestNotice.type) {
      case 0:
        return "日常通知通告";
      case 1:
        return "协查通报";
      case 2:
        return "留言内容";
      default:
        return "";
    }
  };

  return (
    <Flex vertical className="home" gap={24}>
      <Row gutter={16}>
        <Col span={12}>
          <TextPanel title="快捷操作">
            <Row gutter={16}>
              <PermView types={[0, 1]} roles={[1, 2]}>
                <Col span={6}>
                  <Flex
                    vertical
                    align="center"
                    justify="center"
                    gap={12}
                    onClick={() => {
                      navigate("/exam/registration");
                    }}
                    className={classNames("quick-item", `quick-item__${1}`)}
                  >
                    <span className="quick-item-icon">
                      <AuditOutlined />
                    </span>
                    <span className="quick-item-title">报名管理</span>
                  </Flex>
                </Col>
              </PermView>

              <PermView types={[0]} roles={[1]}>
                <Col span={6}>
                  <Flex
                    vertical
                    align="center"
                    justify="center"
                    gap={12}
                    onClick={() => {
                      navigate("/exam/examination");
                    }}
                    className={classNames("quick-item", `quick-item__${2}`)}
                  >
                    <span className="quick-item-icon">
                      <AuditOutlined />
                    </span>
                    <span className="quick-item-title">安排考试</span>
                  </Flex>
                </Col>
              </PermView>

              <PermView types={[0]} roles={[1]}>
                <Col span={6}>
                  <Flex
                    vertical
                    align="center"
                    justify="center"
                    gap={12}
                    onClick={() => {
                      message.success("功能开发中");
                    }}
                    className={classNames("quick-item", `quick-item__${3}`)}
                  >
                    <span className="quick-item-icon">
                      <AuditOutlined />
                    </span>
                    <span className="quick-item-title">许可证查询</span>
                  </Flex>
                </Col>
              </PermView>

              <PermView types={[0, 1]} roles={[1]}>
                <Col span={6}>
                  <Flex
                    vertical
                    align="center"
                    justify="center"
                    gap={12}
                    onClick={() => {
                      navigate("/exam/admission/0");
                    }}
                    className={classNames("quick-item", `quick-item__${4}`)}
                  >
                    <span className="quick-item-icon">
                      <AuditOutlined />
                    </span>
                    <span className="quick-item-title">保安证书查询</span>
                  </Flex>
                </Col>
              </PermView>
            </Row>
          </TextPanel>
        </Col>
        <Col span={12}>
          <PermView types={[0, 1]} roles={[1]}>
            <TextPanel title="通知通告与留言">
              <div
                style={{
                  lineHeight: "32px",
                  height: "100px",
                  overflowY: "auto",
                }}
              >
                {latestNotices.map((latestNotice) => {
                  return (
                    <Flex
                      key={latestNotice.id}
                      justify="space-between"
                      align="center"
                      onClick={() => {
                        // 通知通告管理
                        if (
                          latestNotice.type === 0 ||
                          latestNotice.type === 1
                        ) {
                          if (user.type === 0) {
                            navigate(`/notification/publish`);
                          }
                          if (user.type === 1) {
                            navigate(`/notification/view`);
                          }
                        }
                        // 留言管理
                        if (latestNotice.type === 2) {
                          if (user.type === 0) {
                            navigate(`/message/reply`);
                          }
                          if (user.type === 1) {
                            navigate(`/message/send`);
                          }
                        }
                      }}
                    >
                      <Flex style={{ width: "calc(100% - 160px)" }}>
                        <Typography.Paragraph
                          style={{ margin: 0, padding: 0, cursor: "pointer" }}
                          ellipsis={{
                            rows: 1,
                            symbol: "...",
                            tooltip: `【${getLatestContentType(latestNotice)}】${latestNotice.title}`,
                          }}
                        >
                          {`【${getLatestContentType(latestNotice)}】${latestNotice.title}`}
                        </Typography.Paragraph>
                      </Flex>
                      <Flex style={{ width: "150px" }}>
                        {dayjs(latestNotice.publishedTime).format(
                          "YYYY-MM-DD HH:mm:ss"
                        )}
                      </Flex>
                    </Flex>
                  );
                })}
              </div>
            </TextPanel>
          </PermView>
        </Col>
      </Row>

      {summaryAnalysis && (
        <Row className="system-summary">
          <Col span={24}>
            <TextPanel title="系统概况">
              <Row gutter={12} style={{ paddingTop: "12px" }}>
                <Col span={8}>
                  <Flex vertical>
                    <Flex
                      className="exam-header"
                      align="center"
                      justify="center"
                    >
                      报名与考试情况
                    </Flex>
                    <Flex className="exam-content" vertical gap={4}>
                      <PermView types={[0, 1]} roles={[1, 2]}>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>
                            {`报名情况：报名总人数${summaryAnalysis?.totalExamUser}人（审查通过${summaryAnalysis?.examUserApprovedCount}人，未通过${summaryAnalysis?.examUserRejectedCount}人）`}
                          </span>
                        </Flex>
                      </PermView>

                      <PermView types={[0]} roles={[1, 2]}>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>{`资格审查进度：已审查${summaryAnalysis?.examUserReviewedCount}人，待审查${summaryAnalysis.examUserPendingReviewCount}人`}</span>
                        </Flex>
                      </PermView>

                      <PermView types={[0, 1]} roles={[1]}>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>
                            {`考试安排情况：共安排考试${summaryAnalysis?.totalExamUserScheduleCount}场，安排考试人数${summaryAnalysis?.totalExamUserRecord}人，实际参考人数${summaryAnalysis?.totalUniqueExamUser}人`}
                          </span>
                        </Flex>
                      </PermView>

                      <PermView types={[0, 1]} roles={[1]}>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>{`
                        准考证打印情况：已打印${summaryAnalysis?.admissionPrintedCount}人，待打印${summaryAnalysis?.admissionPendingCount}人`}</span>
                        </Flex>
                      </PermView>

                      <PermView types={[0, 1]} roles={[1]}>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>
                            {`考试成绩统计：合格${summaryAnalysis?.examPassedCount}人，不合格${summaryAnalysis?.examFailedCount}人，弃考${summaryAnalysis?.examAbsentCount}人`}
                          </span>
                        </Flex>
                      </PermView>

                      <PermView types={[0, 1]} roles={[1]}>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>
                            {`已获得保安证人数：${summaryAnalysis?.certificateCount}人`}
                          </span>
                        </Flex>
                      </PermView>
                    </Flex>
                  </Flex>
                </Col>

                <PermView types={[0]} roles={[1]}>
                  <Col span={8}>
                    <Flex vertical>
                      <Flex
                        className="manage-header"
                        align="center"
                        justify="center"
                      >
                        企业管理情况
                      </Flex>
                      <Flex className="manage-content" vertical gap={4}>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>

                          <span>
                            {`企业设立申请情况：申请总数：${summaryAnalysis?.totalApplyEnterprise}家（审核通过${summaryAnalysis?.enterpriseApprovedCount}家，未通过${summaryAnalysis?.enterpriseRejectedCount}家）`}
                          </span>
                        </Flex>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>{`企业设立审核进度：已审核${summaryAnalysis?.enterpriseReviewedCount}家，待审核${summaryAnalysis?.enterprisePendingReviewCount}家`}</span>
                        </Flex>
                        <Flex align="start" gap={8}>
                          <Flex flex="0 0 4px">
                            <span className="point"></span>
                          </Flex>
                          <span>{`已获得许可证企业数：${summaryAnalysis?.enterpriseLicensedCount}家`}</span>
                        </Flex>
                      </Flex>
                    </Flex>
                  </Col>
                </PermView>

                <PermView types={[0, 1]} roles={[1]}>
                  <Col span={8}>
                    <Flex vertical>
                      <Flex
                        className="message-header"
                        align="center"
                        justify="center"
                      >
                        通知通告与沟通情况
                      </Flex>

                      <Flex className="message-content" vertical gap={4}>
                        <PermView types={[0, 1]} roles={[1]}>
                          <Flex align="start" gap={8}>
                            <Flex flex="0 0 4px">
                              <span className="point"></span>
                            </Flex>
                            <span>
                              {`通知通告发布情况：共发布${summaryAnalysis?.totalNotice}条（日常通知${summaryAnalysis?.dailyNoticeCount}条，协查通报${summaryAnalysis?.investigationNoticeCount}条）`}
                            </span>
                          </Flex>
                        </PermView>

                        <PermView types={[0]} roles={[1]}>
                          <Flex align="start" gap={8}>
                            <Flex flex="0 0 4px">
                              <span className="point"></span>
                            </Flex>
                            <span>
                              {`企业留言情况：共收到${summaryAnalysis?.totalMessage}条（已回复${summaryAnalysis?.repliedMessageCount}条，待回复${summaryAnalysis?.unrepliedMessageCount}条）`}
                            </span>
                          </Flex>
                        </PermView>
                      </Flex>
                    </Flex>
                  </Col>
                </PermView>
              </Row>
            </TextPanel>
          </Col>
        </Row>
      )}

      {/* <Row>
        <Col span={24}>
          <PermView types={[0, 1]} roles={[1]}>
            <TextPanel title="考试概况分析">
              <Row gutter={16} className="analysis-pannel">
                <PermView types={[0, 1]} roles={[1]}>
                  <Col span={6} className="analysis-item">
                    <Flex gap={16} className="analysis-item-wrapper">
                      <Flex justify="center" className="analysis-icon">
                        <AuditOutlined />
                      </Flex>
                      <Flex
                        vertical
                        justify="center"
                        gap={6}
                        className="analysis-content"
                      >
                        <span className="analysis-item-title">总考试数</span>
                        <span className="analysis-item-value">
                          {summaryAnalysis.examUserScheduleCount}
                        </span>
                      </Flex>
                    </Flex>
                  </Col>
                </PermView>

                <PermView types={[0, 1]} roles={[1]}>
                  <Col span={6} className="analysis-item">
                    <Flex gap={16} className="analysis-item-wrapper">
                      <Flex justify="center" className="analysis-icon">
                        <AuditOutlined />
                      </Flex>
                      <Flex
                        vertical
                        justify="center"
                        gap={6}
                        className="analysis-content"
                      >
                        <span className="analysis-item-title">
                          保安员报名数
                        </span>
                        <span className="analysis-item-value">
                          {summaryAnalysis?.examUserScheduleRecordCount}
                        </span>
                      </Flex>
                    </Flex>
                  </Col>
                </PermView>

                <PermView types={[0, 1]} roles={[1]}>
                  <Col span={6} className="analysis-item">
                    <Flex gap={16} className="analysis-item-wrapper">
                      <Flex justify="center" className="analysis-icon">
                        <AuditOutlined />
                      </Flex>
                      <Flex
                        vertical
                        justify="center"
                        gap={6}
                        className="analysis-content"
                      >
                        <span className="analysis-item-title">
                          总保安员持证数
                        </span>
                        <span className="analysis-item-value">
                          {summaryAnalysis?.securityGuardCount}
                        </span>
                      </Flex>
                    </Flex>
                  </Col>
                </PermView>

                <PermView types={[0]} roles={[1]}>
                  <Col span={6} className="analysis-item">
                    <Flex gap={16} className="analysis-item-wrapper">
                      <Flex justify="center" className="analysis-icon">
                        <AuditOutlined />
                      </Flex>
                      <Flex
                        vertical
                        justify="center"
                        gap={6}
                        className="analysis-content"
                      >
                        <span className="analysis-item-title">
                          总企业许可证数
                        </span>
                        <span className="analysis-item-value">
                          {summaryAnalysis?.enterpriseCount}
                        </span>
                      </Flex>
                    </Flex>
                  </Col>
                </PermView>
              </Row>
            </TextPanel>
          </PermView>
        </Col>
      </Row> */}
      {/* <PermView types={[0, 1]} roles={[1]}>
        <Row>
          <Col span={24}>
            <TextPanel title="考试证书概况统计">
              <Row gutter={60} style={{ paddingTop: "12px" }}>
                <Col span={6}>
                  <Flex vertical gap={40} justify="center">
                    <Flex vertical gap={16} justify="center">
                      <div style={{ color: "#999999", fontSize: "16px" }}>
                        累计新获得证书数
                      </div>
                      <Space size={36}>
                        <span style={{ color: "#11133e", fontSize: "28px" }}>
                          200
                        </span>
                        <Flex
                          gap={4}
                          style={{ color: "#999999", fontSize: "16px" }}
                          align="center"
                        >
                          <span>周环比</span>
                          <span style={{ color: "#31A996" }}>
                            <CaretUpFilled />
                          </span>
                          <span>12% </span>
                        </Flex>
                      </Space>
                    </Flex>
                    <Flex vertical gap={16} justify="center">
                      <div style={{ color: "#999999", fontSize: "16px" }}>
                        本月新增考试数
                      </div>
                      <Space size={36}>
                        <span style={{ color: "#11133e", fontSize: "28px" }}>
                          60
                        </span>
                        <Flex
                          gap={4}
                          style={{ color: "#999999", fontSize: "16px" }}
                          align="center"
                        >
                          <span>周同比</span>
                          <span style={{ color: "#31A996" }}>
                            <CaretUpFilled />
                          </span>
                          <span>18% </span>
                        </Flex>
                      </Space>
                    </Flex>
                  </Flex>
                </Col>
                <Col span={18}>
                  <Flex vertical gap={12} justify="center">
                    <Flex justify="space-between">
                      <span style={{ fontWeight: "bold", fontSize: "16px" }}>
                        近一月新增获得证书人数
                      </span>
                      <Space>
                        <Radio.Group
                          value={radioValue}
                          onChange={(e) => {
                            console.log(e.target.value);
                            setRadioValue(e.target.value);
                          }}
                        >
                          <Radio.Button value="day">今日</Radio.Button>
                          <Radio.Button value="week">本周</Radio.Button>
                          <Radio.Button value="month">本月</Radio.Button>
                          <Radio.Button value="year">全年</Radio.Button>
                        </Radio.Group>
                        <DatePicker.RangePicker
                          onChange={(value) => {
                            setRadioValue("");
                          }}
                          allowClear={false}
                          value={dateRanges}
                        />
                      </Space>
                    </Flex>
                    <div
                      className="graph"
                      id={chartId}
                      style={{ width: "100%", height: "300px" }}
                    ></div>
                  </Flex>
                </Col>
              </Row>
            </TextPanel>
          </Col>
        </Row>
      </PermView> */}

      <Row>
        <div style={{ height: "1px" }}></div>
      </Row>
      <EditPublishModal
        id={currentLatestNotice.id}
        type="view"
        isEditPublishVisible={isEditPublishVisible}
        setIsEditPublishVisible={setIsEditPublishVisible}
        tableReload={() => {}}
      ></EditPublishModal>
    </Flex>
  );
};

export default Home;
