import React, { useState } from "react";
import { Button, Col, Flex, Form, Input, message, Row, Select } from "antd";
import { useNavigate } from "react-router-dom";
import { useForm } from "antd/lib/form/Form";
import { dataApi } from "@/apis";
import "./index.less";

const Register: React.FC = () => {
  const [form] = useForm();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const handleRegisterClick = () => {
    form.validateFields().then((data) => {
      setIsLoading(true);
      dataApi
        .postRegisterEnterprise({
          serviceType: data.serviceType,
          name: data.name,
          account: data.account,
          password: data.password,
        })
        .then(() => {
          setIsLoading(false);
          message.success("注册成功");
          navigate("/login");
        })
        .catch(() => {
          setIsLoading(false);
        });
    });
  };

  const initialValues = {};

  return (
    <Row className="register">
      <Col span={24} className="register-pannel-col">
        <Flex vertical justify="center" align="center" className="register">
          <div className="register-subtitle">公司账号注册</div>
          <div className="register-pannel">
            <Form
              form={form}
              initialValues={initialValues}
              labelCol={{ span: 5 }}
            >
              <Form.Item
                name="serviceType"
                label="保安服务类型"
                rules={[{ required: true, message: "请选择保安服务类型" }]}
              >
                <Select
                  placeholder="请选择保安服务类型"
                  popupMatchSelectWidth={false}
                  size="large"
                  options={[
                    { value: "0", label: "保安培训单位" },
                    { value: "1", label: "保安服务公司（保安服务分公司）" },
                    { value: "2", label: "武装守护押运" },
                    { value: "3", label: "公司自行招用保安员的单位 " },
                    { value: "4", label: "物业" },
                    { value: "5", label: "跨区域保安服务公司" },
                  ]}
                />
              </Form.Item>
              <Form.Item
                name="name"
                label="公司名称"
                rules={[{ required: true, message: "请输入公司名称" }]}
              >
                <Input
                  size="large"
                  style={{ borderRadius: "10px" }}
                  placeholder="请输入公司名称"
                  autoComplete="off"
                />
              </Form.Item>
              <Form.Item
                name="account"
                label="公司账号"
                rules={[{ required: true, message: "请输入公司账号" }]}
              >
                <Input
                  size="large"
                  style={{ borderRadius: "10px" }}
                  placeholder="请输入公司账号"
                  autoComplete="off"
                />
              </Form.Item>
              <Form.Item
                name="password"
                label={<div style={{ color: "#333333" }}>密码</div>}
                rules={[{ required: true, message: "请输入密码" }]}
              >
                <Input
                  size="large"
                  style={{ borderRadius: "10px" }}
                  placeholder="请输入密码"
                  autoComplete="off"
                />
              </Form.Item>
            </Form>
            <Flex vertical gap={8} style={{ padding: "48px 24px 0" }}>
              <Button
                block
                loading={isLoading}
                type="primary"
                size="large"
                onClick={handleRegisterClick}
              >
                注册
              </Button>
            </Flex>
          </div>
        </Flex>
      </Col>
    </Row>
  );
};

export default Register;
