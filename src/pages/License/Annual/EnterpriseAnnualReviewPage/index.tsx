import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  Input,
  Select,
} from "antd";
import { dataApi } from "@/apis";
import EnterpriseDetailModalProps from "../../components/EnterpriseDetailModal";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const EnterpriseAnnualReviewPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isEnterpriseDetailModalVisible, setIsEnterpriseDetailModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [serviceType, setServiceType] = useState<number>();
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseAnnualReviewPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        serviceType,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleView = (record: any) => {
    navigate(`/license/annual/${record.id}`);
  };

  const handleEnterpriseView = (record: any) => {
    setCurrentRecord(record);
    setIsEnterpriseDetailModalVisible(true);
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "企业名称",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "统一社会信用代码",
      dataIndex: "creditCode",
      minWidth: 132,
      render: (value) => value ?? "-",
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },
    {
      title: "操作",
      key: "action",
      hidden: user?.type === 1,
      render: (_, record) => (
        <Space size="small">
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleEnterpriseView(record)}
          >
            企业详情
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleView(record)}
          >
            年检记录
          </PermButton>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择保安服务类型"
            allowClear
            popupMatchSelectWidth={false}
            style={{ width: 200 }}
            value={serviceType}
            onChange={(val) => {
              setServiceType(val);
            }}
            onSelect={handleSearch}
            onClear={() => {
              setServiceType(undefined);
              handleSearch();
            }}
            options={[
              { value: "", label: "所有类型" },
              { value: 0, label: "保安培训单位" },
              { value: 1, label: "保安服务公司（保安服务分公司）" },
              { value: 2, label: "武装守护押运" },
              { value: 3, label: "公司自行招用保安员的单位" },
              { value: 4, label: "物业" },
              { value: 5, label: "跨区域保安服务公司" },
            ]}
          />
          <Input
            placeholder="请输入企业名称或统一社会信用代码"
            style={{ width: 280 }}
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EnterpriseDetailModalProps
        id={currentRecord?.id}
        isEnterpriseDetailModalVisible={isEnterpriseDetailModalVisible}
        setIsEnterpriseDetailModalVisible={setIsEnterpriseDetailModalVisible}
      />
    </div>
  );
};

export default EnterpriseAnnualReviewPage;
