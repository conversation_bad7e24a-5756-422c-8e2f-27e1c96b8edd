import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { Table, Space, Flex, message } from "antd";
import type { ColumnsType } from "antd/es/table";
import { dataApi } from "@/apis";
import PermButton from "@/components/PermButton";

const EnterpriseAnnualReviewList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);

  const { id } = useParams();

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseAnnualReviewRecordList(id)
      .then((data) => {
        setLoading(false);
        setDataSource(data);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, []);

  const handlePass = (record: any) => {
    dataApi.postSaveEnterpriseAnnualReviewRecord(record.id).catch((err) => {
      message.error(err.message);
    });
  };

  const columns: ColumnsType<any> = [
    {
      title: "序号",
      key: "index",
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: "年检状态",
      dataIndex: "bizStatus",
      render: (value) =>
        ({
          0: "待年检",
          1: "已过期",
          2: "年检通过",
          3: "年检不通过",
        })[value],
    },
    {
      title: "年检类型",
      dataIndex: "reviewType",
      minWidth: 120,
      render: (value) => value ?? "-",
    },
    {
      title: "年检时间",
      dataIndex: "reviewDate",
      minWidth: 120,
      render: (value) => value ?? "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            disabled={![0, 1].includes(record?.bizStatus)}
            onClick={() => handlePass(record)}
          >
            年检通过
          </PermButton>
        </Space>
      ),
    },
  ];

  return (
    <div className="examination">
      <Flex vertical gap={12}>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
        />
      </Flex>
    </div>
  );
};

export default EnterpriseAnnualReviewList;
