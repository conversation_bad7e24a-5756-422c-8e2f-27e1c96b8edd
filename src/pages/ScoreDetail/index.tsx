import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  Table,
  Space,
  message,
  Button,
  Flex,
  Input,
  Select,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import LogModal from "./components/LogModal";
import RegistrationEditModal from "../Registration/components/EditModal";
import { dataApi } from "@/apis";
import "./index.less";
import { dataToExcel } from "@/utils";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const ScoreDetail: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isRegistrationEditModalVisible, setIsRegistrationEditModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  const [stat, setStat] = useState("");
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [isLogModalVisible, setIsLogModalVisible] = useState(false);
  const [recordBizStatus, setRecordBizStatus] = useState(undefined);
  const [searchText, setSearchText] = useState("");
  const { examUserScheduleId } = useParams();
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserScorePage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        examUserScheduleId:
          examUserScheduleId !== "0" ? examUserScheduleId : null,
        recordBizStatus,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));

    if (examUserScheduleId === "0") {
      dataApi.postGetPageStatisticsSummary(4).then((data) => {
        const statText = data
          .map((x) => `${x.label}：${x.value ?? "-"}`)
          .join("；");
        setStat(statText);
      });
    }
  };

  useEffect(getDataSourceRequest, [pagination, examUserScheduleId]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setIsRegistrationEditModalVisible(true);
  };

  const handleClear = () => {
    setRecordBizStatus(undefined);
    setSearchText("");
    handleSearch();
  };

  const handlePhysicalStatus = (record: any) => {
    dataApi
      .postSaveExamUserPhysicalStatus(record.id, 1 - record.physicalStatus)
      .then(() => {
        message.success("操作成功");
        getDataSourceRequest();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const handleLog = (record: any) => {
    setCurrentRecord(record);
    setIsLogModalVisible(true);
  };

  const handleExport = () => {
    setExportLoading(true);
    dataApi
      .postGetExportExamUserScoreList({
        examUserScheduleId:
          examUserScheduleId !== "0" ? examUserScheduleId : null,
        recordBizStatus,
        search: searchText,
      })
      .then((data) => {
        const header = [
          { value: "index", label: "序号" },
          { value: "level", label: "考试名称" },
          { value: "name", label: "姓名" },
          { value: "phone", label: "联系电话" },
          { value: "idCard", label: "身份证号码" },
          { value: "examTime", label: "考试时间" },
          { value: "score", label: "本次考分" },
          { value: "recordBizStatus", label: "状态" },
          { value: "physicalStatus", label: "体能成绩" },
        ];
        dataToExcel(data, {
          header,
          filename: `考试成绩列表-${String(new Date().getTime())}`,
        });
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setExportLoading(false));
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "考试名称",
      dataIndex: "levelDesc",
      render: (value) => value || "-",
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "考试时间",
      dataIndex: "timeRange",
      render: (_, record) =>
        `${record.examDate} ${record.startTime}-${record.endTime}`,
    },
    {
      title: "本次考分",
      dataIndex: "score",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "状态",
      dataIndex: "recordBizStatus",
      render: (value) =>
        ({ 0: "未开始", 1: "进行中", 2: "未通过", 3: "已通过", 4: "弃考" })[
          value
        ],
    },
    {
      title: "体能成绩",
      dataIndex: "physicalStatus",
      width: 90,
      render: (value) => ({ 0: "通过", 1: "不通过" })[value],
    },
    {
      title: "操作",
      key: "action",
      hidden: user?.role === 2,
      render: (_, record) => (
        <>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="physicalStatus"
            onClick={() => handlePhysicalStatus(record)}
          >
            {{ 0: "体能不通过", 1: "体能通过" }[record.physicalStatus]}
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="log"
            onClick={() => handleLog(record)}
          >
            考试日志
          </PermButton>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            考生详情
          </PermButton>
        </>
      ),
    },
  ];

  return (
    <div className="scoreDetail">
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择状态"
            allowClear
            style={{ width: 200 }}
            value={recordBizStatus}
            onChange={(val) => {
              setRecordBizStatus(val);
            }}
            onSelect={handleSearch}
            onClear={handleClear}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "未开始" },
              { value: 1, label: "进行中" },
              { value: 2, label: "未通过" },
              { value: 3, label: "已通过" },
              { value: 4, label: "弃考" },
            ]}
          />
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="primary"
            onClick={handleExport}
            loading={exportLoading}
          >
            导出
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => (
              <Space size="middle">
                {stat && <div className="stat">{stat}</div>}
                <div>共 {x} 条记录</div>
              </Space>
            ),
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <LogModal
        id={currentRecord?.id}
        isLogModalVisible={isLogModalVisible}
        setIsLogModalVisible={setIsLogModalVisible}
      ></LogModal>
      <RegistrationEditModal
        examUserId={currentRecord?.examUserId}
        isEditModalVisible={isRegistrationEditModalVisible}
        setIsEditModalVisible={setIsRegistrationEditModalVisible}
        tableReload={getDataSourceRequest}
        isViewMode
      ></RegistrationEditModal>
    </div>
  );
};

export default ScoreDetail;
