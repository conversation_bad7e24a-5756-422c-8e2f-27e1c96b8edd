import React, { useEffect, useState } from "react";
import { useLocation, useSearchParams } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  Select,
  type TablePaginationConfig,
  Input,
  Modal,
  message,
} from "antd";
import { dataApi } from "@/apis";
import { dataToExcel } from "@/utils";
import dayjs from "dayjs";
import CancelCertModal from "./components/CancelCertModal";
import EditModal from "./components/EditModal";
import AddQuitModal from "./components/AddQuitModal";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const SecurityGuardList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit" | "view">("add");
  const [isCancelCertModalVisible, setIsCancelCertModalVisible] =
    useState(false);
  const [isAddQuitModalVisible, setIsAddQuitModalVisible] = useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [sourceType, setSourceType] = useState<number>();
  const [certificateStatus, setCertificateStatus] = useState<number>();
  const [searchText, setSearchText] = useState("");

  const [searchParams] = useSearchParams();
  const enterpriseId = searchParams.get("enterpriseId");

  // 从路径中提取最后一段作为类型
  const location = useLocation();
  const pathSegments = location.pathname.split("/");
  const type = pathSegments[pathSegments.length - 1];

  const { user } = useAuth();

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseSecurityGuardPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        enterpriseId,
        type: type === "all" ? null : type,
        sourceType,
        certificateStatus,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleAdd = () => {
    setCurrentRecord(null);
    setModalMode("add");
    setIsEditModalVisible(true);
  };

  const handleAddQuit = () => {
    setCurrentRecord(null);
    setIsAddQuitModalVisible(true);
  };

  const handleExport = () => {
    setExportLoading(true);
    dataApi
      .postGetExportEnterpriseSecurityGuardList({
        enterpriseId,
        type: type === "all" ? null : type,
        sourceType,
      })
      .then((data) => {
        const header = getColumns(type).map((c) => ({
          value: c.dataIndex,
          label: c.title,
        }));
        const title = {
          all: "保安员-人员信息",
          "0": "保安员-考试获证人员",
          "1": "保安员-持证人员",
          "2": "保安员-离职人员",
        }[type];
        dataToExcel(data, {
          header,
          filename: `${title}-${String(new Date().getTime())}`,
        });
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setExportLoading(false));
  };

  const handleEdit = (record: any) => {
    setCurrentRecord(record);
    setModalMode("edit");
    setIsEditModalVisible(true);
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setModalMode("view");
    setIsEditModalVisible(true);
  };

  const handleCancelCert = (record: any) => {
    setCurrentRecord(record);
    setIsCancelCertModalVisible(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: "确认删除",
      content: `确定要删除吗？`,
      onOk: () =>
        dataApi
          .postDelEnterpriseSecurityGuard(record.id)
          .then(() => {
            message.success("操作成功");
            getDataSourceRequest();
          })
          .catch((err) => {
            message.error(err.message);
          }),
    });
  };

  const handleRemove = (record: any) => {
    Modal.confirm({
      title: "确认删除",
      content: `确定要移除吗？`,
      onOk: () =>
        dataApi
          .postRemoveResignedEnterpriseSecurityGuard(record.id)
          .then(() => {
            message.success("操作成功");
            getDataSourceRequest();
          })
          .catch((err) => {
            message.error(err.message);
          }),
    });
  };

  const getColumns = (typeParam: string) => {
    const fixedColumnKeys = ["index", "name", "phone", "idCard"];
    const optoinColumnKeys = {
      all: ["certificateNo", "bizStatus", "certificateStatus", "operatorTime"],
      "0": ["certificateNo", "operatorTime"],
      "1": ["certificateNo", "source", "operatorTime"],
      "2": ["bizStatus", "resignedReason", "operatorTime"],
    }[typeParam];
    return [...fixedColumnKeys, ...optoinColumnKeys].map((ck) =>
      columns.find((c) => c.dataIndex === ck)
    );
  };

  const columns = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (_value, _record, index) => {
        return index + 1;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      minWidth: 120,
      render: (value) => value ?? "-",
    },
    {
      title: "证书编号",
      dataIndex: "certificateNo",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "持证类型",
      dataIndex: "source",
      minWidth: 90,
      render: (value) => {
        return {
          0: "本系统持证",
          1: "本系统持证",
          2: "外系统持证",
        }[value];
      },
    },
    {
      title: "职位状态",
      dataIndex: "bizStatus",
      minWidth: 90,
      render: (value) => {
        return {
          0: "在职",
          1: "离职",
        }[value];
      },
    },
    {
      title: "离职原因",
      dataIndex: "resignedReason",
      minWidth: 90,
      render: (value) => ({ 0: "主动离职", 1: "协商离职", 2: "辞退" })[value],
    },
    {
      title: "证书状态",
      dataIndex: "certificateStatus",
      minWidth: 90,
      render: (value) => ({ 0: "正常", 1: "吊销" })[value],
    },
    {
      title: "操作时间",
      dataIndex: "operatorTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
  ];

  const actionColumn = {
    title: "操作",
    key: "action",
    render: (_, record) => (
      <Space size="small">
        {type === "1" && (
          <PermButton
            types={[1]}
            roles={[]}
            type="link"
            size="small"
            onClick={() => handleEdit(record)}
          >
            修改
          </PermButton>
        )}
        <Button type="link" size="small" onClick={() => handleView(record)}>
          详情
        </Button>
        <PermButton
          types={[0]}
          roles={[1]}
          type="link"
          size="small"
          onClick={() => handleCancelCert(record)}
        >
          吊销证书
        </PermButton>
        {type === "1" && (
          <PermButton
            types={[1]}
            roles={[]}
            type="link"
            size="small"
            onClick={() => handleDelete(record)}
          >
            删除
          </PermButton>
        )}
        {type === "2" && (
          <PermButton
            types={[1]}
            roles={[0]}
            type="link"
            size="small"
            onClick={() => handleRemove(record)}
          >
            移除
          </PermButton>
        )}
      </Space>
    ),
  };

  const tableColumns = [...getColumns(type), actionColumn];

  return (
    <div>
      <Flex vertical gap={12}>
        <Space>
          {type === "all" && (
            <Select
              placeholder="请选择证书状态"
              allowClear
              style={{ width: 200 }}
              value={certificateStatus}
              onChange={(val) => {
                setCertificateStatus(val);
              }}
              onSelect={handleSearch}
              onClear={() => {
                setCertificateStatus(undefined);
                handleSearch();
              }}
              options={[
                { value: 0, label: "正常" },
                { value: 1, label: "吊销" },
              ]}
            />
          )}
          {type === "1" && (
            <Select
              placeholder="请选择持证类型"
              allowClear
              style={{ width: 200 }}
              value={sourceType}
              onChange={(val) => {
                setSourceType(val);
              }}
              onSelect={handleSearch}
              onClear={() => {
                setSourceType(undefined);
                handleSearch();
              }}
              options={[
                { value: 0, label: "本系统持证" },
                { value: 1, label: "外系统持证" },
              ]}
            />
          )}
          <Input
            placeholder="请输入姓名或身份证号码"
            style={{ width: 200 }}
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          {type === "1" && (
            <PermButton
              types={[1]}
              roles={[]}
              type="primary"
              onClick={handleAdd}
            >
              添加
            </PermButton>
          )}
          {type === "2" && user?.type === 1 && (
            <Button type="primary" onClick={handleAddQuit}>
              添加
            </Button>
          )}
          <Button type="primary" onClick={handleExport} loading={exportLoading}>
            导出
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={tableColumns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EditModal
        id={currentRecord?.id}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
        mode={modalMode}
      />
      <CancelCertModal
        id={currentRecord?.id}
        isCancelCertModalVisible={isCancelCertModalVisible}
        setIsCancelCertModalVisible={setIsCancelCertModalVisible}
        tableReload={getDataSourceRequest}
      />
      <AddQuitModal
        isAddQuitModalVisible={isAddQuitModalVisible}
        setIsAddQuitModalVisible={setIsAddQuitModalVisible}
        tableReload={getDataSourceRequest}
      />
    </div>
  );
};

export default SecurityGuardList;
