import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Input,
  Modal,
  Flex,
  message,
  type TableColumnsType,
  Form,
  Select,
} from "antd";
import { dataApi } from "@/apis";

interface AddQuitModal {
  isAddQuitModalVisible: boolean;
  setIsAddQuitModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const AddQuitModal: React.FC<AddQuitModal> = (props) => {
  const { isAddQuitModalVisible, setIsAddQuitModalVisible, tableReload } =
    props;
  const [tableLoading, setTableLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [searchText, setSearchText] = useState("");
  const [searchTrigger, setSearchTrigger] = useState<number>(0);
  const [reason, setReason] = useState<number>();

  useEffect(() => {
    if (isAddQuitModalVisible && searchTrigger > 0) {
      getDataSourceRequest();
    }
  }, [isAddQuitModalVisible, searchTrigger]);

  const getDataSourceRequest = () => {
    setTableLoading(true);

    dataApi
      .postGetEnterpriseSecurityGuardList({
        search: searchText,
      })
      .then((data) => {
        setTableLoading(false);
        setDataSource(data);
      })
      .finally(() => setTableLoading(false));
  };

  const handleSearch = () => {
    setSearchTrigger((prev) => {
      return prev + 1;
    });
  };

  const handleClear = () => {
    setSearchText("");
  };

  const onFinish = () => {
    if (!(reason >= 0)) {
      message.error("请选择离职原因");
    }

    if (selectedKeys?.length > 0) {
      setSaveLoading(true);
      dataApi
        .postSaveResignedEnterpriseSecurityGuard({
          id: selectedKeys[0],
          resignedReason: reason,
        })
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .catch((err) => {
          message.error(err.message);
        })
        .finally(() => setSaveLoading(false));
    } else {
      message.error("请选择要添加的离职人员");
    }
  };

  const onCancel = () => {
    setIsAddQuitModalVisible(false);
    setSearchText("");
    setSearchTrigger(0);
    setReason(undefined);
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (_value, _record, index) => {
        return index + 1;
      },
    },
    {
      title: "证书编号",
      dataIndex: "certificateNo",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "保安员等级",
      minWidth: 120,
      dataIndex: "certificateLevel",
      render: (value) =>
        ({ 1: "初级保安员", 2: "中级保安员", 3: "高级保安员" })[value],
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      minWidth: 120,
      render: (value) => value || "-",
    },
    {
      title: "报名单位",
      dataIndex: "workLocation",
      minWidth: 90,
      render: (value) => value || "-",
    },
  ];

  return (
    <Modal
      title="离职人员添加"
      open={isAddQuitModalVisible}
      width={960}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          loading={saveLoading}
          onClick={onFinish}
        >
          确定添加
        </Button>,
      ]}
    >
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            style={{ width: 200 }}
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          <Button type="primary" onClick={getDataSourceRequest}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 479px)" }}
          dataSource={dataSource}
          loading={tableLoading}
          rowSelection={{
            hideSelectAll: true,
            type: "radio",
            selectedRowKeys: selectedKeys,
            onChange: (selectedRowKeys) =>
              setSelectedKeys(selectedRowKeys as Array<string>),
          }}
          pagination={false}
        />
        <Form.Item label="离职原因" required>
          <Select
            value={reason}
            onChange={setReason}
            placeholder="请选择离职原因"
            allowClear
            options={[
              { value: 0, label: "主动离职" },
              { value: 1, label: "协商离职" },
              { value: 2, label: "辞退" },
            ]}
          />
        </Form.Item>
      </Flex>
    </Modal>
  );
};

export default AddQuitModal;
