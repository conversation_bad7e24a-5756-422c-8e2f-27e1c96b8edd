import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router";
import { useNavigate } from "react-router-dom";
import { Table, Space, Button, Flex, type TableColumnsType } from "antd";
import { dataApi } from "@/apis";
import { IExamUserScheduleSummary } from "@/apis/data.model";

const SecurityGuardType: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<IExamUserScheduleSummary[]>([]);
  const [searchParams] = useSearchParams();
  const enterpriseId = searchParams.get("enterpriseId");

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseSecurityGuardSummaryList(enterpriseId)
      .then((data) => {
        setLoading(false);
        seteDataSource(data);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, []);

  const handleView = (record?: any) => {
    const typeParam = record?.type ?? "all";
    navigate(
      `/general/securityGuard/type/${typeParam}?enterpriseId=${enterpriseId}`
    );
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 90,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "保安员分类管理",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "保安员数",
      dataIndex: "count",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <>
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            详情
          </Button>
        </>
      ),
    },
  ];

  return (
    <div>
      <Flex vertical gap={12}>
        <Space>
          <Button type="primary" onClick={() => handleView()}>
            人员信息查询
          </Button>
        </Space>
        <Table
          rowKey="type"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
        />
      </Flex>
    </div>
  );
};

export default SecurityGuardType;
