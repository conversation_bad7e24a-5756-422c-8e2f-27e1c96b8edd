import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  Space,
  Button,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  Select,
  Input,
} from "antd";
import { dataApi } from "@/apis";
import { IExamUserScheduleSummary } from "@/apis/data.model";
import EnterpriseDetailModalProps from "@/pages/License/components/EnterpriseDetailModal";
import dayjs from "dayjs";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const EnterpriseCustomerSummary: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<IExamUserScheduleSummary[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isEnterpriseDetailModalVisible, setIsEnterpriseDetailModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [serviceType, setServiceType] = useState<number>();
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseCustomerSummaryPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        serviceType,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleEnterpriseView = (record: any) => {
    setCurrentRecord(record);
    setIsEnterpriseDetailModalVisible(true);
  };

  const handleView = (record: any) => {
    navigate(`/general/customer/${record.id}`);
  };

  const handleViewAll = () => navigate(`/general/customer/all`);

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "企业名称",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "统一社会信用代码",
      dataIndex: "creditCode",
      minWidth: 132,
      render: (value) => value ?? "-",
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },
    {
      title: "客户数量",
      dataIndex: "customerCount",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "操作时间",
      dataIndex: "operatorTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleEnterpriseView(record)}
          >
            企业详情
          </PermButton>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            客户详情
          </PermButton>
        </>
      ),
    },
  ];

  return (
    <div>
      <Flex vertical gap={12}>
        {user?.type === 0 && (
          <Space>
            <Select
              placeholder="请选择保安服务类型"
              allowClear
              popupMatchSelectWidth={false}
              style={{ width: 200 }}
              value={serviceType}
              onChange={(val) => {
                setServiceType(val);
              }}
              onSelect={handleSearch}
              onClear={() => {
                setServiceType(undefined);
                handleSearch();
              }}
              options={[
                { value: "", label: "所有类型" },
                { value: 0, label: "保安培训单位" },
                { value: 1, label: "保安服务公司（保安服务分公司）" },
                { value: 2, label: "武装守护押运" },
                { value: 3, label: "公司自行招用保安员的单位" },
                { value: 4, label: "物业" },
                { value: 5, label: "跨区域保安服务公司" },
              ]}
            />
            <Input
              placeholder="请输入企业名称或统一社会信用代码"
              style={{ width: 280 }}
              allowClear
              onChange={(e) => {
                setSearchText(e.target.value);
              }}
              onPressEnter={handleSearch}
              onClear={handleSearch}
            />
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
            <PermButton
              types={[0]}
              roles={[1, 2]}
              type="primary"
              onClick={handleViewAll}
            >
              客户列表查询
            </PermButton>
          </Space>
        )}
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EnterpriseDetailModalProps
        id={currentRecord?.id}
        isEnterpriseDetailModalVisible={isEnterpriseDetailModalVisible}
        setIsEnterpriseDetailModalVisible={setIsEnterpriseDetailModalVisible}
      />
    </div>
  );
};

export default EnterpriseCustomerSummary;
