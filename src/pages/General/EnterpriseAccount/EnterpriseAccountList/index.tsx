import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  Select,
  type TableColumnsType,
  type TablePaginationConfig,
  Input,
  Modal,
  message,
} from "antd";
import { dataApi } from "@/apis";
import dayjs from "dayjs";
import PermButton from "@/components/PermButton";
import EditModal from "../components/EditModal";
import ResetPasswordModal from "@/components/ResetPasswordModal";

const EnterpriseAccountList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [bizStatus, setBizStatus] = useState<number>();
  const [enterpriseBizStatus, setEnterpriseBizStatus] = useState<number>();
  const { serviceType } = useParams();
  const [searchText, setSearchText] = useState("");

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseAccountPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        serviceType: serviceType === "all" ? null : serviceType,
        bizStatus,
        enterpriseBizStatus,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleEdit = (record: any) => {
    setCurrentRecord(record);
    setIsEditModalVisible(true);
  };

  const handleResetPassword = (record: any) => {
    setCurrentRecord(record);
    setIsResetPasswordModalVisible(true);
  };

  const handleCancel = (record: any) => {
    Modal.confirm({
      title: "确认注销",
      content: `确定要注销企业账号"${record.enterpriseName}"吗？注销后该账号将无法登录。`,
      onOk: () =>
        dataApi
          .postFreezeEnterpriseAccount({ id: record.id })
          .then(() => {
            message.success("注销成功");
            getDataSourceRequest();
          })
          .catch((err) => {
            message.error(err.message);
          }),
    });
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: "确认删除",
      content: `确定要删除企业账号"${record.enterpriseName}"吗？删除后数据将无法恢复。`,
      onOk: () =>
        dataApi
          .postDelEnterpriseAccount({ id: record.id })
          .then(() => {
            message.success("删除成功");
            getDataSourceRequest();
          })
          .catch((err) => {
            message.error(err.message);
          }),
    });
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "企业名称",
      dataIndex: "enterpriseName",
      render: (value) => value ?? "-",
    },
    {
      title: "状态",
      dataIndex: "showEnterpriseBizStatus",
      minWidth: 90,
      render: (value) =>
        ({
          0: "账号注册",
          1: "审查中",
          2: "审查通过",
          3: "审查未通过",
        })[value],
    },
    {
      title: "企业账号",
      dataIndex: "account",
      minWidth: 80,
      render: (value) => value ?? "-",
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "账号状态",
      dataIndex: "bizStatus",
      minWidth: 90,
      render: (value) =>
        ({
          0: "正常",
          1: "注销",
        })[value],
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            disabled={![0, 1, 2].includes(record?.enterpriseBizStatus)}
            onClick={() => handleEdit(record)}
          >
            修改
          </PermButton>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleResetPassword(record)}
          >
            重置密码
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleCancel(record)}
          >
            注销
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleDelete(record)}
          >
            删除
          </PermButton>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择状态"
            allowClear
            style={{ width: 200 }}
            value={enterpriseBizStatus}
            onChange={(val) => {
              setEnterpriseBizStatus(val);
            }}
            onSelect={handleSearch}
            onClear={() => {
              setEnterpriseBizStatus(undefined);
              handleSearch();
            }}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "账号注册" },
              { value: 1, label: "审查中" },
              { value: 2, label: "审查通过" },
              { value: 3, label: "审查未通过" },
            ]}
          />
          <Select
            placeholder="请选择账号状态"
            allowClear
            style={{ width: 200 }}
            value={bizStatus}
            onChange={(val) => {
              setBizStatus(val);
            }}
            onSelect={handleSearch}
            onClear={() => {
              setBizStatus(undefined);
              handleSearch();
            }}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "正常" },
              { value: 1, label: "注销" },
            ]}
          />
          <Input
            placeholder="请输入企业名称或企业账号"
            style={{ width: 280 }}
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EditModal
        currentRecord={currentRecord}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
      />
      <ResetPasswordModal
        id={currentRecord?.id}
        isResetPasswordModalVisible={isResetPasswordModalVisible}
        setIsResetPasswordModalVisible={setIsResetPasswordModalVisible}
        tableReload={getDataSourceRequest}
      />
    </div>
  );
};

export default EnterpriseAccountList;
