import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { Table, Space, Flex } from "antd";
import type { ColumnsType } from "antd/es/table";
import { dataApi } from "@/apis";
import PermButton from "@/components/PermButton";
import ResetPasswordModal from "@/components/ResetPasswordModal";

const EnterpriseAccountSummary: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] =
    useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseServiceTypeSummaryList()
      .then((data) => {
        setLoading(false);
        setDataSource(data);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, []);

  const handleViewAll = () => {
    navigate(`/general/account/all`);
  };

  const handleView = (record: any) => {
    navigate(`/general/account/${record.serviceType}`);
  };

  const handleResetPassword = (record: any) => {
    setCurrentRecord(record);
    setIsResetPasswordModalVisible(true);
  };

  const columns: ColumnsType<any> = [
    {
      title: "序号",
      key: "index",
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },

    {
      title: "企业注册数",
      dataIndex: "applyCount",
      minWidth: 100,
    },
    {
      title: "获得许可证数",
      dataIndex: "approvedCount",
      minWidth: 120,
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleView(record)}
          >
            详情
          </PermButton>
          <PermButton
            types={[1]}
            roles={[]}
            type="link"
            size="small"
            onClick={() => handleResetPassword(record)}
          >
            重置密码
          </PermButton>
        </Space>
      ),
    },
  ];

  return (
    <div className="examination">
      <Flex vertical gap={12}>
        <Space>
          <PermButton
            types={[0]}
            roles={[1, 2]}
            type="primary"
            onClick={handleViewAll}
          >
            企业账号查询
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
        />
      </Flex>
      <ResetPasswordModal
        id={currentRecord?.id}
        isResetPasswordModalVisible={isResetPasswordModalVisible}
        setIsResetPasswordModalVisible={setIsResetPasswordModalVisible}
        tableReload={getDataSourceRequest}
      />
    </div>
  );
};

export default EnterpriseAccountSummary;
