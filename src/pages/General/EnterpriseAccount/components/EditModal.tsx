import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface EditModalProps {
  currentRecord?: any;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const EditModal: React.FC<EditModalProps> = (props) => {
  const {
    currentRecord,
    isEditModalVisible,
    setIsEditModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isEditModalVisible && currentRecord) {
      form.setFieldsValue({
        id: currentRecord.id,
        serviceType: currentRecord.serviceType,
        name: currentRecord.enterpriseName,
        account: currentRecord.account,
      });
    }
  }, [isEditModalVisible, currentRecord, form]);

  const onFinish = (values: any) => {
    setLoading(true);
    dataApi
      .postEditEnterpriseAccount({
        id: values.id,
        name: values.name,
        account: values.account,
      })
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="编辑企业人员"
      open={isEditModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 120 } }}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            name="serviceType"
            label="保安服务类型"
            rules={[{ required: true, message: "请选择保安服务类型" }]}
          >
            <Select
              placeholder="请选择保安服务类型"
              options={[
                { value: 0, label: "保安培训单位" },
                { value: 1, label: "保安服务公司（保安服务分公司）" },
                { value: 2, label: "武装守护押运" },
                { value: 3, label: "公司自行招用保安员的单位" },
                { value: 4, label: "物业" },
                { value: 5, label: "跨区域保安服务公司" },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="name"
            label="企业名称"
            rules={[{ required: true, message: "请输入企业名称" }]}
          >
            <Input placeholder="请输入企业名称" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="account"
            label="企业账号"
            rules={[{ required: true, message: "请输入企业账号" }]}
          >
            <Input placeholder="请输入企业账号" autoComplete="off" />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default EditModal;
