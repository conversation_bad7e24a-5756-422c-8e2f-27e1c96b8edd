import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Button,
  Flex,
  Input,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import ReviewModal from "./components/ReviewModal";
import BatchReviewModal from "./components/BatchReviewModal";
import { IExamUser } from "@/apis/data.model";
import "./index.less";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const Qualification: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<IExamUser[]>([]);
  const [currentRecord, setCurrentRecord] = useState<IExamUser>();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [total, setTotal] = useState(0);
  const [stat, setStat] = useState("");
  const [isReviewModalVisible, setIsReviewModalVisible] = useState(false);
  const [isBatchReviewModalVisible, setIsBatchReviewModalVisible] =
    useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        bizStatus: 0,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));

    dataApi.postGetPageStatisticsSummary(1).then((data) => {
      const statText = data.map((x) => `${x.label}：${x.value ?? "-"}`).join("；");
      setStat(statText);
    });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleRowSelectionChange = (selectedRowKeys) => {
    setSelectedKeys(selectedRowKeys);
  };

  const handleReview = (record: IExamUser) => {
    setCurrentRecord(record);
    setIsReviewModalVisible(true);
  };

  const handleBatchReview = () => {
    setIsBatchReviewModalVisible(true);
  };

  const columns: TableColumnsType<IExamUser> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "状态",
      dataIndex: "bizStatus",
      render: (value) =>
        ({ 0: "待审查", 1: "审查通过", 2: "审查未通过" })[value],
    },
    {
      title: "操作",
      key: "action",
      hidden: user?.type === 1,
      render: (_, record) => (
        <>
          <PermButton
            types={[0]}
            roles={[1, 2]}
            type="link"
            size="small"
            key="review"
            onClick={() => handleReview(record)}
          >
            审查
          </PermButton>
        </>
      ),
    },
  ];

  return (
    <div className="qualification">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button
            type="primary"
            onClick={handleBatchReview}
            disabled={selectedKeys.length === 0}
          >
            批量审查
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => (
              <Space size="middle">
                <div className="stat">{stat}</div>
                <div>共 {x} 条记录</div>
              </Space>
            ),
            total,
          }}
          onChange={handleTableChange}
          rowSelection={{
            selectedRowKeys: selectedKeys,
            onChange: handleRowSelectionChange,
            getCheckboxProps: (record: IExamUser) => ({
              disabled: record.bizStatus !== 0,
            }),
          }}
        />
      </Flex>
      <ReviewModal
        id={currentRecord?.id}
        isReviewModalVisible={isReviewModalVisible}
        setIsReviewModalVisible={setIsReviewModalVisible}
        tableReload={getDataSourceRequest}
      ></ReviewModal>
      <BatchReviewModal
        selectedKeys={selectedKeys}
        setSelectedKeys={setSelectedKeys}
        isBatchReviewModalVisible={isBatchReviewModalVisible}
        setIsBatchReviewModalVisible={setIsBatchReviewModalVisible}
        tableReload={getDataSourceRequest}
      ></BatchReviewModal>
    </div>
  );
};

export default Qualification;
