import React, { Dispatch, SetStateAction, useState } from "react";
import { Button, Form, Input, Modal, Select, message, Alert } from "antd";
import { dataApi } from "@/apis";

interface BatchReviewModalProps {
  selectedKeys: string[];
  setSelectedKeys: Dispatch<SetStateAction<string[]>>;
  isBatchReviewModalVisible: boolean;
  setIsBatchReviewModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const BatchReviewModal: React.FC<BatchReviewModalProps> = (props) => {
  const {
    selectedKeys,
    setSelectedKeys,
    isBatchReviewModalVisible,
    setIsBatchReviewModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    setLoading(true);
    dataApi
      .postSaveReviewExamUser({ ...values, ids: selectedKeys })
      .then(() => {
        message.success("操作成功");
        setSelectedKeys([]);
        tableReload();
        onCancel();
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsBatchReviewModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="资格审查"
      open={isBatchReviewModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          保存
        </Button>,
      ]}
    >
      <Form
        preserve={false}
        form={form}
        onFinish={onFinish}
        name="modal"
        labelAlign="right"
        labelCol={{ style: { width: 120 } }}
      >
        <Form.Item>
          <Alert message="您正在进行批量审查操作！" type="info" showIcon />
        </Form.Item>
        <Form.Item
          name="reviewStatus"
          label="审查状态"
          rules={[{ required: true, message: "请选择审查状态" }]}
        >
          <Select
            placeholder="请选择审查状态"
            options={[
              { value: 0, label: "审查通过" },
              { value: 1, label: "审查未通过" },
            ]}
          />
        </Form.Item>
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.reviewStatus !== curValues.reviewStatus
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("reviewStatus") === 1 ? (
              <Form.Item
                name="reviewContent"
                label="审查未通过理由"
                rules={[{ required: true }]}
              >
                <Input.TextArea rows={2} placeholder="请输入审查未通过理由" />
              </Form.Item>
            ) : null
          }
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default BatchReviewModal;
