import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Input,
  Alert,
  Modal,
  Flex,
  message,
  type TableColumnsType,
} from "antd";
import { dataApi } from "@/apis";
import { IExamUser } from "@/apis/data.model";
import dayjs from "dayjs";

interface AddModal {
  examUserScheduleId: string;
  level: number;
  isAddModalVisible: boolean;
  setIsAddModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const AddModal: React.FC<AddModal> = (props) => {
  const {
    examUserScheduleId,
    level,
    isAddModalVisible,
    setIsAddModalVisible,
    tableReload,
  } = props;
  const [tableLoading, setTableLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [searchText, setSearchText] = useState("");
  const [registrationUnit, setRegistrationUnit] = useState("");
  const [searchTrigger, setSearchTrigger] = useState<number>(0);

  useEffect(() => {
    if (isAddModalVisible && level && (searchTrigger > 0 || level === 1)) {
      getDataSourceRequest();
    }
  }, [isAddModalVisible, searchTrigger]);

  const getDataSourceRequest = () => {
    setTableLoading(true);

    dataApi
      .postGetPendingExamUserList({
        examUserScheduleId,
        level,
        registrationUnit,
        search: searchText,
      })
      .then((data) => {
        setTableLoading(false);
        setDataSource(data);
      })
      .finally(() => setTableLoading(false));
  };

  const handleSearch = () => {
    setSearchTrigger((prev) => {
      return prev + 1;
    });
  };

  const handleClear = () => {
    setSearchText("");
    setRegistrationUnit("");
  };

  const onFinish = () => {
    if (selectedKeys?.length > 0) {
      setSaveLoading(true);
      dataApi
        .postSaveExamUser2ExamUserSchedule(examUserScheduleId, selectedKeys)
        .then(() => {
          message.success("操作成功");
          tableReload();
          setIsAddModalVisible(false);
        })
        .catch((err) => {
          message.error(err.message);
        })
        .finally(() => setSaveLoading(false));
    } else {
      message.warning("请选择要添加的考试人员");
    }
  };

  const onCancel = () => {
    setIsAddModalVisible(false);
    setSearchText("");
    setRegistrationUnit("");
    setSearchTrigger(0);
  };

  const columns: TableColumnsType<IExamUser> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "审查时间",
      dataIndex: "reviewTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "状态",
      dataIndex: "bizStatus",
      minWidth: 60,
      render: (value) =>
        ({ 0: "待审查", 1: "审查通过", 2: "审查未通过" })[value],
    },
    {
      title: "考试类型",
      dataIndex: "examAttemptCount",
      render: (value) => `第 ${value} 次考试`,
    },
  ];

  return (
    <Modal
      title="添加考试人员"
      open={isAddModalVisible}
      width={960}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          loading={saveLoading}
          onClick={onFinish}
        >
          确定添加
        </Button>,
      ]}
    >
      <Flex vertical gap={12}>
        {level !== 1 && (
          <Alert
            message="中/高级考试请输入报名单位或人员信息进行查询"
            type="info"
            showIcon
          />
        )}
        <Space>
          {level !== 1 && (
            <Input
              placeholder="请输入报名单位"
              allowClear
              value={registrationUnit}
              onChange={(e) => {
                setRegistrationUnit(e.target.value);
              }}
              onPressEnter={handleSearch}
              onClear={handleClear}
            />
          )}
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          <Button type="primary" onClick={getDataSourceRequest}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 479px)" }}
          dataSource={dataSource}
          loading={tableLoading}
          rowSelection={{
            selectedRowKeys: selectedKeys,
            onChange: (selectedRowKeys) =>
              setSelectedKeys(selectedRowKeys as Array<string>),
          }}
          pagination={false}
        />
      </Flex>
    </Modal>
  );
};

export default AddModal;
