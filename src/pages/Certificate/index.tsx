import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  Space,
  Button,
  Flex,
  DatePicker,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import { IExamUserScheduleSummary } from "@/apis/data.model";
import "./index.less";
import ReceiveViewModal from "./components/ReceiveViewModal";
import ReceiveSaveModal from "./components/ReceiveSaveModal";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const Certificate: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<IExamUserScheduleSummary[]>([]);
  const [currentRecord, setCurrentRecord] =
    useState<IExamUserScheduleSummary>();
  const [total, setTotal] = useState(0);
  const [stat, setStat] = useState("");
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [isReceiveViewModalVisible, setIsReceiveViewModalVisible] =
    useState(false);
  const [isReceiveSaveModalVisible, setIsReceiveSaveModalVisible] =
    useState(false);
  const [examDate, setExamDate] = useState<any>();
  const { user } = useAuth();

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserScheduleCertificateSummaryPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        examDate: examDate,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });

    dataApi.postGetPageStatisticsSummary(5).then((data) => {
      const statText = data.map((x) => `${x.label}：${x.value ?? "-"}`).join("；");
      setStat(statText);
    });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleReceiveSave = (record: IExamUserScheduleSummary) => {
    setCurrentRecord(record);
    setIsReceiveSaveModalVisible(true);
  };

  const handleView = (record: IExamUserScheduleSummary) => {
    navigate(`/exam/certificate/detail/${record.id}`);
  };

  const handleViewAll = () => navigate(`/exam/certificate/detail/0`);

  const handleViewReissue = () => navigate(`/exam/certificate/reissue`);

  const columns: TableColumnsType<IExamUserScheduleSummary> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "考试名称",
      dataIndex: "levelDesc",
      render: (value) => value || "-",
    },
    {
      title: "考试时间",
      dataIndex: "timeRange",
      render: (_, record) =>
        `${record.examDate} ${record.startTime}-${record.endTime}`,
    },
    {
      title: "考试人数",
      dataIndex: "examUserCount",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "获得证书人数",
      dataIndex: "examUserCertificateCount",
      minWidth: 132,
      render: (value) => value ?? "-",
    },
    {
      title: "操作",
      key: "action",
      hidden: user?.role === 2,
      render: (_, record) => (
        <>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            详情
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="receive"
            onClick={() => handleReceiveSave(record)}
            disabled={Number(record.examUserCertificateCount) === 0}
          >
            领取登记
          </PermButton>
        </>
      ),
    },
  ];

  return (
    <div className="certificate">
      <Flex vertical gap={12}>
        <Space>
          <DatePicker
            placeholder="请选择考试时间"
            allowClear
            style={{ width: 200 }}
            onChange={(date) => {
              setExamDate(date);
              handleSearch();
            }}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button type="primary" onClick={handleViewAll}>
            证书 - 人员查询
          </Button>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="primary"
            onClick={() => setIsReceiveViewModalVisible(true)}
          >
            领取查询
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={handleViewReissue}
          >
            证书补打
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => (
              <Space size="middle">
                <div className="stat">{stat}</div>
                <div>共 {x} 条记录</div>
              </Space>
            ),
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <ReceiveViewModal
        isReceiveViewModalVisible={isReceiveViewModalVisible}
        setIsReceiveViewModalVisible={setIsReceiveViewModalVisible}
      ></ReceiveViewModal>
      <ReceiveSaveModal
        type={0}
        source="receiver"
        id={currentRecord?.id}
        isReceiveSaveModalVisible={isReceiveSaveModalVisible}
        setIsReceiveSaveModalVisible={setIsReceiveSaveModalVisible}
        tableReload={getDataSourceRequest}
      ></ReceiveSaveModal>
    </div>
  );
};

export default Certificate;
