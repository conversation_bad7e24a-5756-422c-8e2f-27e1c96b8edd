import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Form,
  Input,
  Modal,
  Select,
  Flex,
  DatePicker,
  TimePicker,
  message,
} from "antd";
import { dataApi } from "@/apis";
import { IExamAddress, IExamUserScheduleSaveParams } from "@/apis/data.model";
import dayjs from "dayjs";

interface EditModal {
  currentRecord?: IExamUserScheduleSaveParams;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const EditModal: React.FC<EditModal> = (props) => {
  const {
    currentRecord,
    isEditModalVisible,
    setIsEditModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [addressOptions, setAddressOptions] = useState<Array<IExamAddress>>();
  const [form] = Form.useForm();

  useEffect(() => {
    if (isEditModalVisible) {
      dataApi.postGetExamAddressList().then(setAddressOptions);
    }
    if (isEditModalVisible && currentRecord?.id) {
      form.setFieldsValue(currentRecord);
      form.setFieldValue(
        "examDate",
        dayjs(currentRecord.examDate, "YYYY-MM-DD")
      );
      form.setFieldValue("timeRange", [
        dayjs(currentRecord.startTime, "HH:mm"),
        dayjs(currentRecord.endTime, "HH:mm"),
      ]);
    }
  }, [isEditModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);

    const { examDate, timeRange, ...params } = values;
    params.examDate = examDate.format("YYYY-MM-DD");
    params.startTime = timeRange[0].format("HH:mm");
    params.endTime = timeRange[1].format("HH:mm");

    if (currentRecord?.id) {
      dataApi
        .postUpdateExamUserSchedule(params)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .catch((err) => {
          message.error(err.message);
        })
        .finally(() => setLoading(false));
    } else {
      dataApi
        .postSaveExamUserSchedule(params)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .catch((err) => {
          message.error(err.message);
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title={currentRecord?.id ? "编辑考试安排" : "添加考试安排"}
      open={isEditModalVisible}
      width={800}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ paddingTop: 24, paddingRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item name="id" hidden>
            <Input></Input>
          </Form.Item>
          <Form.Item
            name="level"
            label="考试名称"
            rules={[{ required: true, message: "请选择考试名称" }]}
          >
            <Select
              placeholder="请选择考试名称"
              options={[
                { value: 1, label: "初级保安考试" },
                { value: 2, label: "中级保安考试" },
                { value: 3, label: "高级保安考试" },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="examAddressId"
            label="考试地点"
            rules={[{ required: true, message: "请选择考试地点" }]}
          >
            <Select
              placeholder="请选择考试地点"
              fieldNames={{ label: "name", value: "id" }}
              options={addressOptions}
            />
          </Form.Item>
          <Flex align="center" gap={12}>
            <Form.Item
              name="examDate"
              label="考试时间"
              rules={[{ required: true, message: "请选择考试日期" }]}
            >
              <DatePicker allowClear={false} />
            </Form.Item>
            <Form.Item
              name="timeRange"
              rules={[{ required: true, message: "请选择考试时间" }]}
            >
              <TimePicker.RangePicker format="HH:mm" needConfirm={false} />
            </Form.Item>
          </Flex>
        </Form>
      </div>
    </Modal>
  );
};

export default EditModal;
