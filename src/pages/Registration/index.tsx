import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Popconfirm,
  message,
  Button,
  Flex,
  Select,
  Input,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import EditModal from "./components/EditModal";
import BatchAddModal from "./components/BatchAddModal";
import { IExamUser } from "@/apis/data.model";
import "./index.less";
import dayjs from "dayjs";
import { dataToExcel } from "@/utils";
import { baseUrl } from "@/config";
import PermButton from "@/components/PermButton";

const Registration: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<IExamUser[]>([]);
  const [currentRecord, setCurrentRecord] = useState<IExamUser>();
  const [isViewMode, setIsViewMode] = useState<boolean>(false);
  const [total, setTotal] = useState(0);
  const [stat, setStat] = useState("");
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isBatchAddModalVisible, setIsBatchAddModalVisible] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [bizStatus, setBizStatus] = useState(undefined);
  const [searchText, setSearchText] = useState("");

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        bizStatus: bizStatus,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });

    dataApi.postGetPageStatisticsSummary(0).then((data) => {
      const statText = data
        .map((x) => `${x.label}：${x.value ?? "-"}`)
        .join("；");
      setStat(statText);
    });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleClear = () => {
    setBizStatus(undefined);
    setSearchText("");
    handleSearch();
  };

  const handleAdd = () => {
    setCurrentRecord(undefined);
    setIsViewMode(false);
    setIsEditModalVisible(true);
  };

  const handleEdit = (record: IExamUser) => {
    setCurrentRecord(record);
    setIsViewMode(false);
    setIsEditModalVisible(true);
  };

  const handleDelete = (record: IExamUser) => {
    dataApi
      .postDelExamUser(record.id)
      .then(() => {
        message.success("操作成功");
        getDataSourceRequest();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const handleView = (record: IExamUser) => {
    setCurrentRecord(record);
    setIsViewMode(true);
    setIsEditModalVisible(true);
  };

  const handleExport = () => {
    setExportLoading(true);
    dataApi
      .postGetExportExamUserList({
        bizStatus: bizStatus,
        search: searchText,
      })
      .then((data) => {
        const header = [
          { value: "index", label: "序号" },
          { value: "name", label: "姓名" },
          { value: "phone", label: "联系电话" },
          { value: "idCard", label: "身份证号码" },
          { value: "bizStatus", label: "状态" },
          { value: "operatorTime", label: "操作时间" },
          { value: "registrationUnit", label: "报名单位" },
        ];
        dataToExcel(data, {
          header,
          filename: `考试人员列表-${String(new Date().getTime())}`,
        });
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setExportLoading(false));
  };

  const columns: TableColumnsType<IExamUser> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "状态",
      dataIndex: "bizStatus",
      render: (value) =>
        ({ 0: "待审查", 1: "审查通过", 2: "审查未通过" })[value],
    },
    {
      title: "操作时间",
      dataIndex: "operatorTime",
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <>
          <PermButton
            types={[0, 1]}
            roles={[1, 2]}
            type="link"
            size="small"
            key="edit"
            onClick={() => handleEdit(record)}
          >
            修改
          </PermButton>
          <Popconfirm title="确定要删除" onConfirm={() => handleDelete(record)}>
            <PermButton
              types={[0, 1]}
              roles={[1, 2]}
              type="link"
              size="small"
              key="del"
            >
              删除
            </PermButton>
          </Popconfirm>
          <PermButton
            types={[0, 1]}
            roles={[1, 2]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            考生详情
          </PermButton>
        </>
      ),
    },
  ];

  return (
    <div className="registration">
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择审查状态"
            allowClear
            style={{ width: 200 }}
            value={bizStatus}
            onChange={(val) => {
              setBizStatus(val);
            }}
            onSelect={handleSearch}
            onClear={handleClear}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "待审查" },
              { value: 1, label: "审查通过" },
              { value: 2, label: "审查未通过" },
            ]}
          />
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <PermButton
            types={[0, 1]}
            roles={[1, 2]}
            type="primary"
            onClick={handleAdd}
          >
            添加
          </PermButton>
          <Button
            type="primary"
            onClick={() => {
              setIsBatchAddModalVisible(true);
            }}
          >
            批量添加
          </Button>
          <Button type="primary" onClick={handleExport} loading={exportLoading}>
            导出
          </Button>
          <PermButton
            types={[0, 1]}
            roles={[1, 2]}
            type="primary"
            href={`${baseUrl}/api/console/file/templateDownload`}
            download="信息导入样表.xlsx"
          >
            xlsx样表下载
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => (
              <Space size="middle">
                <div className="stat">{stat}</div>
                <div>共 {x} 条记录</div>
              </Space>
            ),
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EditModal
        examUserId={currentRecord?.id}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
        isViewMode={isViewMode}
      ></EditModal>
      <BatchAddModal
        isBatchAddModalVisible={isBatchAddModalVisible}
        setIsBatchAddModalVisible={setIsBatchAddModalVisible}
        tableReload={getDataSourceRequest}
      ></BatchAddModal>
    </div>
  );
};

export default Registration;
