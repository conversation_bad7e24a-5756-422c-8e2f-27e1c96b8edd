import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Input,
  Modal,
  Flex,
  message,
  type TableColumnsType,
  Form,
  Select,
} from "antd";
import { dataApi } from "@/apis";

interface ReissusSaveModal {
  isReissusSaveModalVisible: boolean;
  setIsReissusSaveModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ReissusSaveModal: React.FC<ReissusSaveModal> = (props) => {
  const {
    isReissusSaveModalVisible,
    setIsReissusSaveModalVisible,
    tableReload,
  } = props;
  const [tableLoading, setTableLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [searchText, setSearchText] = useState("");
  const [searchTrigger, setSearchTrigger] = useState<number>(0);
  const [reason, setReason] = useState<number>();

  useEffect(() => {
    if (isReissusSaveModalVisible && searchTrigger > 0) {
      getDataSourceRequest();
    }
  }, [isReissusSaveModalVisible, searchTrigger]);

  const getDataSourceRequest = () => {
    setTableLoading(true);

    dataApi
      .postGetExamUserCertificateList({
        search: searchText,
      })
      .then((data) => {
        setTableLoading(false);
        setDataSource(data);
      })
      .finally(() => setTableLoading(false));
  };

  const handleSearch = () => {
    setSearchTrigger((prev) => {
      return prev + 1;
    });
  };

  const handleClear = () => {
    setSearchText("");
  };

  const onFinish = () => {
    if (!(reason >= 0)) {
      message.error("请选择补打原因");
    }

    if (selectedKeys?.length > 0) {
      setSaveLoading(true);
      dataApi
        .postSaveExamUserCertificateReissue({
          id: selectedKeys[0],
          applyReason: reason,
        })
        .then(() => {
          message.success("操作成功");
          tableReload();
          setIsReissusSaveModalVisible(false);
        })
        .catch((err) => {
          message.error(err.message);
        })
        .finally(() => setSaveLoading(false));
    } else {
      message.error("请选择要补打证书的保安员");
    }
  };

  const onCancel = () => {
    setIsReissusSaveModalVisible(false);
    setSearchText("");
    setSearchTrigger(0);
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (_value, _record, index) => {
        return index + 1;
      },
    },
    {
      title: "证书编号",
      dataIndex: "certificateNo",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "保安员等级",
      minWidth: 120,
      dataIndex: "level",
      render: (value) =>
        ({ 0: "初级保安员", 1: "中级保安员", 2: "高级保安员" })[value],
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      minWidth: 120,
      render: (value) => value || "-",
    },
    {
      title: "报名单位",
      dataIndex: "registrationUnit",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "发证日期",
      dataIndex: "certificateDate",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "是否打印证书",
      dataIndex: "certificatePrintFlag",
      minWidth: 132,
      render: (value) => ({ 0: "否", 1: "是" })[value],
    },
  ];

  return (
    <Modal
      title="证书补打登记"
      open={isReissusSaveModalVisible}
      width={960}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          loading={saveLoading}
          onClick={onFinish}
        >
          确认
        </Button>,
      ]}
    >
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            style={{ width: 200 }}
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          <Button type="primary" onClick={getDataSourceRequest}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 479px)" }}
          dataSource={dataSource}
          loading={tableLoading}
          rowSelection={{
            hideSelectAll: true,
            type: "radio",
            selectedRowKeys: selectedKeys,
            onChange: (selectedRowKeys) =>
              setSelectedKeys(selectedRowKeys as Array<string>),
          }}
          pagination={false}
        />
        <Form.Item label="补打原因" required>
          <Select
            value={reason}
            onChange={setReason}
            placeholder="请选择补打原因"
            allowClear
            options={[
              { value: 0, label: "遗失" },
              { value: 1, label: "损坏" },
              { value: 2, label: "其他" },
            ]}
          />
        </Form.Item>
      </Flex>
    </Modal>
  );
};

export default ReissusSaveModal;
