import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  Input,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import ReceiveSaveModal from "../Certificate/components/ReceiveSaveModal";
import PrintCertificateButton from "../CertificateDetail/PrintCertificateButton";
import RegistrationEditModal from "../Registration/components/EditModal";
import ReissusSaveModal from "./ReissusSaveModal";
import PermView from "@/components/PermView";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";
import dayjs from "dayjs";

const CertificateReissue: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isRegistrationEditModalVisible, setIsRegistrationEditModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [isReceiveSaveModalVisible, setIsReceiveSaveModalVisible] =
    useState(false);
  const [isReissusSaveModalVisible, setIsReissusSaveModalVisible] =
    useState(false);
  const [searchText, setSearchText] = useState("");
  const { examUserScheduleId } = useParams();
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserCertificateReissuePage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));
  };

  useEffect(getDataSourceRequest, [pagination, examUserScheduleId]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setIsRegistrationEditModalVisible(true);
  };

  const handleReceiveSave = (record: any) => {
    setCurrentRecord(record);
    setIsReceiveSaveModalVisible(true);
  };

  const handleReissusSave = (record: any) => {
    setCurrentRecord(record);
    setIsReissusSaveModalVisible(true);
  };

  const afterPrint = () => {
    getDataSourceRequest();
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "证书编号",
      dataIndex: "certificateNo",
      render: (value) => value || "-",
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "保安员等级",
      dataIndex: "level",
      render: (value) =>
        ({
          0: "初级保安员",
          1: "中级保安员",
          2: "高级保安员",
        })[value],
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "报名单位",
      dataIndex: "registrationUnit",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "补打登记日期",
      dataIndex: "applyTime",
      minWidth: 90,
      render: (value) => (value ? dayjs(value).format("YYYY-MM-DD") : "-"),
    },
    {
      title: "补打原因",
      dataIndex: "applyReason",
      minWidth: 90,
      render: (value) =>
        ({
          0: "遗失",
          1: "损坏",
          2: "其他",
        })[value],
    },
    {
      title: "是否补打证书",
      dataIndex: "reissueCertificatePrintFlag",
      minWidth: 132,
      render: (value) => ({ 0: "否", 1: "是" })[value],
    },
    {
      title: "补打日期",
      dataIndex: "reissueCertificateDate",
      minWidth: 90,
      render: (value) => (value ? dayjs(value).format("YYYY-MM-DD") : "-"),
    },
    {
      title: "操作",
      key: "action",
      width: 240,
      hidden: user?.role === 2,
      render: (_, record) => (
        <Flex align="center">
          <PermView types={[0]} roles={[1]}>
            <PrintCertificateButton
              type="link"
              size="small"
              key="print"
              id={record.examUserScheduleRecordId}
              afterPrint={afterPrint}
            >
              打印证书
            </PrintCertificateButton>
          </PermView>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="receive"
            onClick={() => handleReceiveSave(record)}
          >
            补领登记
          </PermButton>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            考生详情
          </PermButton>
        </Flex>
      ),
    },
  ];

  return (
    <div className="certificateReissue">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={handleReissusSave}
          >
            补打登记
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <ReceiveSaveModal
        type={1}
        source="reissue"
        id={currentRecord?.id}
        isReceiveSaveModalVisible={isReceiveSaveModalVisible}
        setIsReceiveSaveModalVisible={setIsReceiveSaveModalVisible}
        tableReload={getDataSourceRequest}
      ></ReceiveSaveModal>
      <RegistrationEditModal
        examUserId={currentRecord?.examUserId}
        isEditModalVisible={isRegistrationEditModalVisible}
        setIsEditModalVisible={setIsRegistrationEditModalVisible}
        tableReload={getDataSourceRequest}
        isViewMode
      ></RegistrationEditModal>
      <ReissusSaveModal
        isReissusSaveModalVisible={isReissusSaveModalVisible}
        setIsReissusSaveModalVisible={setIsReissusSaveModalVisible}
        tableReload={getDataSourceRequest}
      />
    </div>
  );
};

export default CertificateReissue;
